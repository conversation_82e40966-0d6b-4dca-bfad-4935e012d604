"""
Service for gathering Discord server context for AI processing.
"""
import discord
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
from src.utils.logger import bot_logger

class ServerContextService:
    """Gathers and formats Discord server information for AI processing."""
    
    @staticmethod
    async def get_server_context(guild: discord.Guild) -> Dict[str, Any]:
        """
        Gather comprehensive server context for AI processing.
        
        Args:
            guild: Discord guild to analyze
            
        Returns:
            Dictionary containing server structure and metadata
        """
        try:
            context = {
                "server_info": {
                    "name": guild.name,
                    "id": guild.id,
                    "member_count": guild.member_count,
                    "created_at": guild.created_at.isoformat(),
                    "owner_id": guild.owner_id
                },
                "categories": [],
                "channels": {
                    "text": [],
                    "voice": [],
                    "stage": [],
                    "uncategorized": []
                },
                "roles": [],
                "permissions_summary": {}
            }
            
            # Gather categories and their channels
            for category in guild.categories:
                category_data = {
                    "name": category.name,
                    "id": category.id,
                    "position": category.position,
                    "channels": []
                }
                
                for channel in category.channels:
                    channel_data = ServerContextService._get_channel_data(channel)
                    category_data["channels"].append(channel_data)
                
                context["categories"].append(category_data)
            
            # Gather uncategorized channels
            for channel in guild.channels:
                if channel.category is None and not isinstance(channel, discord.CategoryChannel):
                    channel_data = ServerContextService._get_channel_data(channel)
                    
                    if isinstance(channel, discord.TextChannel):
                        context["channels"]["text"].append(channel_data)
                    elif isinstance(channel, discord.VoiceChannel):
                        context["channels"]["voice"].append(channel_data)
                    elif isinstance(channel, discord.StageChannel):
                        context["channels"]["stage"].append(channel_data)
                    else:
                        context["channels"]["uncategorized"].append(channel_data)
            
            # Gather roles (excluding @everyone and bot roles)
            for role in guild.roles:
                if role != guild.default_role and not role.managed:
                    role_data = {
                        "name": role.name,
                        "id": role.id,
                        "color": str(role.color),
                        "position": role.position,
                        "permissions": {
                            "administrator": role.permissions.administrator,
                            "manage_guild": role.permissions.manage_guild,
                            "manage_channels": role.permissions.manage_channels,
                            "manage_roles": role.permissions.manage_roles,
                            "manage_messages": role.permissions.manage_messages,
                            "kick_members": role.permissions.kick_members,
                            "ban_members": role.permissions.ban_members
                        },
                        "member_count": len(role.members)
                    }
                    context["roles"].append(role_data)
            
            # Generate permissions summary
            context["permissions_summary"] = ServerContextService._analyze_permissions(guild)
            
            bot_logger.debug(f"Generated server context for {guild.name}")
            return context
            
        except Exception as e:
            bot_logger.error(f"Error gathering server context: {e}", exc_info=True)
            return {
                "server_info": {"name": guild.name, "id": guild.id},
                "error": "Failed to gather complete server context"
            }
    
    @staticmethod
    def _get_channel_data(channel) -> Dict[str, Any]:
        """Extract relevant data from a Discord channel."""
        base_data = {
            "name": channel.name,
            "id": channel.id,
            "position": channel.position,
            "type": channel.type.name
        }
        
        # Add channel-specific data
        if isinstance(channel, discord.TextChannel):
            base_data.update({
                "topic": channel.topic,
                "slowmode_delay": channel.slowmode_delay,
                "nsfw": channel.nsfw
            })
        elif isinstance(channel, discord.VoiceChannel):
            base_data.update({
                "bitrate": channel.bitrate,
                "user_limit": channel.user_limit,
                "connected_members": len(channel.members)
            })
        
        # Add permission overwrites summary
        overwrites = []
        for target, overwrite in channel.overwrites.items():
            if isinstance(target, discord.Role):
                target_name = f"@{target.name}"
            else:
                target_name = str(target)
            
            overwrites.append({
                "target": target_name,
                "allow": [perm for perm, value in overwrite if value is True],
                "deny": [perm for perm, value in overwrite if value is False]
            })
        
        base_data["permission_overwrites"] = overwrites
        return base_data
    
    @staticmethod
    def _analyze_permissions(guild: discord.Guild) -> Dict[str, Any]:
        """Analyze overall permission structure of the server."""
        summary = {
            "admin_roles": [],
            "moderator_roles": [],
            "public_channels": 0,
            "restricted_channels": 0,
            "voice_channels": 0
        }
        
        # Analyze roles
        for role in guild.roles:
            if role.permissions.administrator:
                summary["admin_roles"].append(role.name)
            elif (role.permissions.manage_messages or 
                  role.permissions.manage_channels or 
                  role.permissions.kick_members):
                summary["moderator_roles"].append(role.name)
        
        # Analyze channels
        for channel in guild.channels:
            if isinstance(channel, discord.VoiceChannel):
                summary["voice_channels"] += 1
            elif isinstance(channel, discord.TextChannel):
                # Check if channel is public (everyone can view)
                everyone_perms = channel.overwrites_for(guild.default_role)
                if everyone_perms.view_channel is not False:
                    summary["public_channels"] += 1
                else:
                    summary["restricted_channels"] += 1
        
        return summary
    
    @staticmethod
    def format_context_for_ai(context: Dict[str, Any]) -> str:
        """Format server context into a readable string for AI processing."""
        try:
            formatted = f"SERVER: {context['server_info']['name']}\n"
            formatted += f"MEMBERS: {context['server_info']['member_count']}\n\n"
            
            # Categories and channels
            if context['categories']:
                formatted += "CATEGORIES:\n"
                for cat in context['categories']:
                    formatted += f"  {cat['name']}:\n"
                    for ch in cat['channels']:
                        formatted += f"    #{ch['name']} ({ch['type']})\n"
                formatted += "\n"
            
            # Uncategorized channels
            uncategorized = (context['channels']['text'] + 
                           context['channels']['voice'] + 
                           context['channels']['stage'])
            if uncategorized:
                formatted += "UNCATEGORIZED CHANNELS:\n"
                for ch in uncategorized:
                    formatted += f"  #{ch['name']} ({ch['type']})\n"
                formatted += "\n"
            
            # Roles
            if context['roles']:
                formatted += "ROLES:\n"
                for role in context['roles']:
                    formatted += f"  {role['name']} ({role['member_count']} members)\n"
                formatted += "\n"
            
            # Permissions summary
            perm_summary = context.get('permissions_summary', {})
            if perm_summary:
                formatted += "PERMISSIONS SUMMARY:\n"
                formatted += f"  Admin roles: {', '.join(perm_summary.get('admin_roles', []))}\n"
                formatted += f"  Moderator roles: {', '.join(perm_summary.get('moderator_roles', []))}\n"
                formatted += f"  Public channels: {perm_summary.get('public_channels', 0)}\n"
                formatted += f"  Restricted channels: {perm_summary.get('restricted_channels', 0)}\n"
                formatted += f"  Voice channels: {perm_summary.get('voice_channels', 0)}\n"
            
            return formatted
            
        except Exception as e:
            bot_logger.error(f"Error formatting context for AI: {e}", exc_info=True)
            return f"SERVER: {context.get('server_info', {}).get('name', 'Unknown')}\nError formatting context."

    @staticmethod
    async def analyze_server_health(guild: discord.Guild) -> Dict[str, Any]:
        """
        Perform comprehensive server health analysis with scoring.

        Returns:
            Dictionary with health scores and detailed analysis
        """
        try:
            analysis = {
                "overall_score": 0,
                "category_scores": {},
                "recommendations": [],
                "server_type": "unknown",
                "strengths": [],
                "weaknesses": [],
                "detailed_analysis": {}
            }

            # Analyze different aspects
            organization_score = await ServerContextService._analyze_organization(guild)
            security_score = await ServerContextService._analyze_security(guild)
            engagement_score = await ServerContextService._analyze_engagement(guild)
            moderation_score = await ServerContextService._analyze_moderation(guild)

            # Calculate overall score
            scores = {
                "organization": organization_score,
                "security": security_score,
                "engagement": engagement_score,
                "moderation": moderation_score
            }

            analysis["category_scores"] = scores
            analysis["overall_score"] = sum(scores.values()) / len(scores)

            # Detect server type
            analysis["server_type"] = await ServerContextService._detect_server_type(guild)

            # Generate recommendations based on scores
            analysis["recommendations"] = await ServerContextService._generate_recommendations(guild, scores)

            # Identify strengths and weaknesses
            analysis["strengths"] = [category for category, score in scores.items() if score >= 80]
            analysis["weaknesses"] = [category for category, score in scores.items() if score < 60]

            # Detailed analysis
            analysis["detailed_analysis"] = {
                "organization": await ServerContextService._get_organization_details(guild),
                "security": await ServerContextService._get_security_details(guild),
                "engagement": await ServerContextService._get_engagement_details(guild),
                "moderation": await ServerContextService._get_moderation_details(guild)
            }

            return analysis

        except Exception as e:
            bot_logger.error(f"Error in server health analysis: {e}", exc_info=True)
            return {
                "overall_score": 0,
                "error": "Failed to analyze server health",
                "recommendations": ["Please try the analysis again"]
            }

    @staticmethod
    async def _analyze_organization(guild: discord.Guild) -> float:
        """Analyze server organization and return score 0-100."""
        score = 0
        max_score = 100

        # Check for categories (30 points)
        if guild.categories:
            score += 30
            # Bonus for good category usage
            categorized_channels = sum(len(cat.channels) for cat in guild.categories)
            total_channels = len([ch for ch in guild.channels if not isinstance(ch, discord.CategoryChannel)])
            if total_channels > 0 and categorized_channels / total_channels > 0.8:
                score += 10

        # Check for essential channels (40 points)
        essential_channels = ['welcome', 'rules', 'announcements', 'general']
        found_essential = 0
        for channel in guild.text_channels:
            if any(essential in channel.name.lower() for essential in essential_channels):
                found_essential += 1
        score += (found_essential / len(essential_channels)) * 40

        # Check for voice channels (20 points)
        if guild.voice_channels:
            score += 20

        # Check for proper naming conventions (10 points)
        proper_names = 0
        for channel in guild.channels:
            if not isinstance(channel, discord.CategoryChannel):
                # Check for proper naming (lowercase, hyphens, no spaces)
                if channel.name.islower() and ' ' not in channel.name:
                    proper_names += 1

        if guild.channels:
            non_category_channels = [ch for ch in guild.channels if not isinstance(ch, discord.CategoryChannel)]
            if non_category_channels:
                score += (proper_names / len(non_category_channels)) * 10

        return min(score, max_score)

    @staticmethod
    async def _analyze_security(guild: discord.Guild) -> float:
        """Analyze server security and return score 0-100."""
        score = 0

        # Check verification level (25 points)
        verification_scores = {
            discord.VerificationLevel.none: 0,
            discord.VerificationLevel.low: 10,
            discord.VerificationLevel.medium: 15,
            discord.VerificationLevel.high: 20,
            discord.VerificationLevel.highest: 25
        }
        score += verification_scores.get(guild.verification_level, 0)

        # Check for moderation roles (25 points)
        mod_roles = 0
        for role in guild.roles:
            if (role.permissions.kick_members or role.permissions.ban_members or
                role.permissions.manage_messages):
                mod_roles += 1
        score += min(mod_roles * 8, 25)

        # Check for proper @everyone permissions (25 points)
        everyone_perms = guild.default_role.permissions
        if not everyone_perms.administrator and not everyone_perms.manage_guild:
            score += 25

        # Check for channel permission overwrites (25 points)
        channels_with_overwrites = 0
        for channel in guild.channels:
            if channel.overwrites:
                channels_with_overwrites += 1

        if guild.channels:
            score += (channels_with_overwrites / len(guild.channels)) * 25

        return min(score, 100)

    @staticmethod
    async def _analyze_engagement(guild: discord.Guild) -> float:
        """Analyze server engagement features and return score 0-100."""
        score = 0

        # Check for variety of channel types (30 points)
        has_text = bool(guild.text_channels)
        has_voice = bool(guild.voice_channels)
        has_stage = bool(guild.stage_channels)

        if has_text: score += 15
        if has_voice: score += 10
        if has_stage: score += 5

        # Check for community features (20 points)
        if guild.premium_tier > 0:
            score += 10
        if guild.features:
            score += min(len(guild.features) * 2, 10)

        # Check for custom emojis (15 points)
        if guild.emojis:
            score += min(len(guild.emojis) * 2, 15)

        # Check for roles variety (20 points)
        non_managed_roles = [r for r in guild.roles if not r.managed and r != guild.default_role]
        if non_managed_roles:
            score += min(len(non_managed_roles) * 3, 20)

        # Check for member engagement indicators (15 points)
        if guild.member_count > 10:
            score += 5
        if guild.member_count > 50:
            score += 5
        if guild.member_count > 100:
            score += 5

        return min(score, 100)

    @staticmethod
    async def _analyze_moderation(guild: discord.Guild) -> float:
        """Analyze moderation setup and return score 0-100."""
        score = 0

        # Check for moderation channels (30 points)
        mod_channels = ['mod', 'admin', 'staff', 'reports', 'logs']
        found_mod_channels = 0
        for channel in guild.text_channels:
            if any(mod_term in channel.name.lower() for mod_term in mod_channels):
                found_mod_channels += 1
        score += min(found_mod_channels * 10, 30)

        # Check for moderation roles (40 points)
        admin_roles = sum(1 for role in guild.roles if role.permissions.administrator)
        mod_roles = sum(1 for role in guild.roles if
                       (role.permissions.kick_members or role.permissions.ban_members)
                       and not role.permissions.administrator)

        score += min(admin_roles * 15, 20)  # Max 20 for admin roles
        score += min(mod_roles * 10, 20)    # Max 20 for mod roles

        # Check for proper role hierarchy (30 points)
        if guild.roles:
            # Check if admin roles are at the top
            sorted_roles = sorted(guild.roles, key=lambda r: r.position, reverse=True)
            top_roles_are_admin = any(role.permissions.administrator for role in sorted_roles[:3])
            if top_roles_are_admin:
                score += 15

            # Check for role separation
            if len(guild.roles) > 3:  # More than @everyone, bot role, and one other
                score += 15

        return min(score, 100)

    @staticmethod
    async def _detect_server_type(guild: discord.Guild) -> str:
        """Detect the likely purpose/type of the server."""
        # Analyze channel names and server features
        channel_names = [ch.name.lower() for ch in guild.channels]

        gaming_keywords = ['gaming', 'game', 'minecraft', 'valorant', 'league', 'csgo', 'wow', 'raid', 'guild']
        business_keywords = ['meeting', 'project', 'work', 'office', 'team', 'department', 'hr', 'sales']
        education_keywords = ['class', 'homework', 'study', 'lesson', 'course', 'assignment', 'teacher', 'student']
        community_keywords = ['general', 'chat', 'discussion', 'community', 'social', 'hangout']

        scores = {
            'gaming': sum(1 for name in channel_names if any(kw in name for kw in gaming_keywords)),
            'business': sum(1 for name in channel_names if any(kw in name for kw in business_keywords)),
            'education': sum(1 for name in channel_names if any(kw in name for kw in education_keywords)),
            'community': sum(1 for name in channel_names if any(kw in name for kw in community_keywords))
        }

        # Return the type with highest score, or 'general' if tied/no clear winner
        max_score = max(scores.values())
        if max_score == 0:
            return 'general'

        return max(scores, key=scores.get)

    @staticmethod
    async def _generate_recommendations(guild: discord.Guild, scores: Dict[str, float]) -> List[str]:
        """Generate specific recommendations based on analysis scores."""
        recommendations = []

        # Organization recommendations
        if scores['organization'] < 70:
            if not guild.categories:
                recommendations.append("Create categories to organize your channels (e.g., 'General', 'Gaming', 'Voice Channels')")

            essential_channels = ['welcome', 'rules', 'announcements']
            existing_channels = [ch.name.lower() for ch in guild.text_channels]
            missing_essential = [ch for ch in essential_channels if not any(ch in existing for existing in existing_channels)]

            if missing_essential:
                recommendations.append(f"Add essential channels: {', '.join(missing_essential)}")

            if not guild.voice_channels:
                recommendations.append("Add voice channels for member interaction")

        # Security recommendations
        if scores['security'] < 70:
            if guild.verification_level == discord.VerificationLevel.none:
                recommendations.append("Enable server verification to prevent spam and raids")

            mod_roles = sum(1 for role in guild.roles if role.permissions.kick_members or role.permissions.ban_members)
            if mod_roles == 0:
                recommendations.append("Create moderation roles with appropriate permissions")

            if guild.default_role.permissions.mention_everyone:
                recommendations.append("Remove @everyone mention permissions to prevent spam")

        # Engagement recommendations
        if scores['engagement'] < 70:
            if not guild.emojis:
                recommendations.append("Add custom emojis to enhance server personality")

            if len(guild.roles) <= 2:  # Only @everyone and bot roles
                recommendations.append("Create member roles to build community hierarchy")

            if not guild.stage_channels and guild.member_count > 20:
                recommendations.append("Consider adding stage channels for events and announcements")

        # Moderation recommendations
        if scores['moderation'] < 70:
            mod_channels = ['mod-chat', 'admin-logs', 'reports']
            existing_channels = [ch.name.lower() for ch in guild.text_channels]
            missing_mod = [ch for ch in mod_channels if not any(ch.replace('-', '') in existing.replace('-', '') for existing in existing_channels)]

            if missing_mod:
                recommendations.append(f"Add moderation channels: {', '.join(missing_mod)}")

            if not any(role.permissions.administrator for role in guild.roles if not role.managed):
                recommendations.append("Assign administrator permissions to trusted members")

        return recommendations

    @staticmethod
    async def _get_organization_details(guild: discord.Guild) -> Dict[str, Any]:
        """Get detailed organization analysis."""
        return {
            "total_channels": len([ch for ch in guild.channels if not isinstance(ch, discord.CategoryChannel)]),
            "categories": len(guild.categories),
            "categorized_channels": sum(len(cat.channels) for cat in guild.categories),
            "text_channels": len(guild.text_channels),
            "voice_channels": len(guild.voice_channels),
            "stage_channels": len(guild.stage_channels),
            "has_welcome": any('welcome' in ch.name.lower() for ch in guild.text_channels),
            "has_rules": any('rule' in ch.name.lower() for ch in guild.text_channels),
            "has_announcements": any('announce' in ch.name.lower() for ch in guild.text_channels)
        }

    @staticmethod
    async def _get_security_details(guild: discord.Guild) -> Dict[str, Any]:
        """Get detailed security analysis."""
        return {
            "verification_level": guild.verification_level.name,
            "admin_roles": sum(1 for role in guild.roles if role.permissions.administrator),
            "mod_roles": sum(1 for role in guild.roles if
                           (role.permissions.kick_members or role.permissions.ban_members)
                           and not role.permissions.administrator),
            "everyone_can_mention": guild.default_role.permissions.mention_everyone,
            "everyone_can_manage": guild.default_role.permissions.manage_guild,
            "channels_with_overwrites": sum(1 for ch in guild.channels if ch.overwrites),
            "mfa_required": guild.mfa_level > 0
        }

    @staticmethod
    async def _get_engagement_details(guild: discord.Guild) -> Dict[str, Any]:
        """Get detailed engagement analysis."""
        return {
            "member_count": guild.member_count,
            "premium_tier": guild.premium_tier,
            "boost_count": guild.premium_subscription_count,
            "custom_emojis": len(guild.emojis),
            "total_roles": len(guild.roles),
            "non_managed_roles": len([r for r in guild.roles if not r.managed]),
            "features": list(guild.features),
            "has_banner": guild.banner is not None,
            "has_icon": guild.icon is not None
        }

    @staticmethod
    async def _get_moderation_details(guild: discord.Guild) -> Dict[str, Any]:
        """Get detailed moderation analysis."""
        mod_channel_keywords = ['mod', 'admin', 'staff', 'log', 'report']
        mod_channels = [ch for ch in guild.text_channels
                       if any(keyword in ch.name.lower() for keyword in mod_channel_keywords)]

        return {
            "moderation_channels": len(mod_channels),
            "admin_roles": sum(1 for role in guild.roles if role.permissions.administrator),
            "moderator_roles": sum(1 for role in guild.roles if
                                 (role.permissions.kick_members or role.permissions.ban_members)
                                 and not role.permissions.administrator),
            "roles_with_manage_messages": sum(1 for role in guild.roles if role.permissions.manage_messages),
            "audit_log_enabled": True,  # Always available in Discord
            "slowmode_channels": sum(1 for ch in guild.text_channels if ch.slowmode_delay > 0)
        }
