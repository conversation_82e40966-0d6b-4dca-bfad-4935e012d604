"""
Service for gathering Discord server context for AI processing.
"""
import discord
from typing import Dict, List, Any
from src.utils.logger import bot_logger

class ServerContextService:
    """Gathers and formats Discord server information for AI processing."""
    
    @staticmethod
    async def get_server_context(guild: discord.Guild) -> Dict[str, Any]:
        """
        Gather comprehensive server context for AI processing.
        
        Args:
            guild: Discord guild to analyze
            
        Returns:
            Dictionary containing server structure and metadata
        """
        try:
            context = {
                "server_info": {
                    "name": guild.name,
                    "id": guild.id,
                    "member_count": guild.member_count,
                    "created_at": guild.created_at.isoformat(),
                    "owner_id": guild.owner_id
                },
                "categories": [],
                "channels": {
                    "text": [],
                    "voice": [],
                    "stage": [],
                    "uncategorized": []
                },
                "roles": [],
                "permissions_summary": {}
            }
            
            # Gather categories and their channels
            for category in guild.categories:
                category_data = {
                    "name": category.name,
                    "id": category.id,
                    "position": category.position,
                    "channels": []
                }
                
                for channel in category.channels:
                    channel_data = ServerContextService._get_channel_data(channel)
                    category_data["channels"].append(channel_data)
                
                context["categories"].append(category_data)
            
            # Gather uncategorized channels
            for channel in guild.channels:
                if channel.category is None and not isinstance(channel, discord.CategoryChannel):
                    channel_data = ServerContextService._get_channel_data(channel)
                    
                    if isinstance(channel, discord.TextChannel):
                        context["channels"]["text"].append(channel_data)
                    elif isinstance(channel, discord.VoiceChannel):
                        context["channels"]["voice"].append(channel_data)
                    elif isinstance(channel, discord.StageChannel):
                        context["channels"]["stage"].append(channel_data)
                    else:
                        context["channels"]["uncategorized"].append(channel_data)
            
            # Gather roles (excluding @everyone and bot roles)
            for role in guild.roles:
                if role != guild.default_role and not role.managed:
                    role_data = {
                        "name": role.name,
                        "id": role.id,
                        "color": str(role.color),
                        "position": role.position,
                        "permissions": {
                            "administrator": role.permissions.administrator,
                            "manage_guild": role.permissions.manage_guild,
                            "manage_channels": role.permissions.manage_channels,
                            "manage_roles": role.permissions.manage_roles,
                            "manage_messages": role.permissions.manage_messages,
                            "kick_members": role.permissions.kick_members,
                            "ban_members": role.permissions.ban_members
                        },
                        "member_count": len(role.members)
                    }
                    context["roles"].append(role_data)
            
            # Generate permissions summary
            context["permissions_summary"] = ServerContextService._analyze_permissions(guild)
            
            bot_logger.debug(f"Generated server context for {guild.name}")
            return context
            
        except Exception as e:
            bot_logger.error(f"Error gathering server context: {e}", exc_info=True)
            return {
                "server_info": {"name": guild.name, "id": guild.id},
                "error": "Failed to gather complete server context"
            }
    
    @staticmethod
    def _get_channel_data(channel) -> Dict[str, Any]:
        """Extract relevant data from a Discord channel."""
        base_data = {
            "name": channel.name,
            "id": channel.id,
            "position": channel.position,
            "type": channel.type.name
        }
        
        # Add channel-specific data
        if isinstance(channel, discord.TextChannel):
            base_data.update({
                "topic": channel.topic,
                "slowmode_delay": channel.slowmode_delay,
                "nsfw": channel.nsfw
            })
        elif isinstance(channel, discord.VoiceChannel):
            base_data.update({
                "bitrate": channel.bitrate,
                "user_limit": channel.user_limit,
                "connected_members": len(channel.members)
            })
        
        # Add permission overwrites summary
        overwrites = []
        for target, overwrite in channel.overwrites.items():
            if isinstance(target, discord.Role):
                target_name = f"@{target.name}"
            else:
                target_name = str(target)
            
            overwrites.append({
                "target": target_name,
                "allow": [perm for perm, value in overwrite if value is True],
                "deny": [perm for perm, value in overwrite if value is False]
            })
        
        base_data["permission_overwrites"] = overwrites
        return base_data
    
    @staticmethod
    def _analyze_permissions(guild: discord.Guild) -> Dict[str, Any]:
        """Analyze overall permission structure of the server."""
        summary = {
            "admin_roles": [],
            "moderator_roles": [],
            "public_channels": 0,
            "restricted_channels": 0,
            "voice_channels": 0
        }
        
        # Analyze roles
        for role in guild.roles:
            if role.permissions.administrator:
                summary["admin_roles"].append(role.name)
            elif (role.permissions.manage_messages or 
                  role.permissions.manage_channels or 
                  role.permissions.kick_members):
                summary["moderator_roles"].append(role.name)
        
        # Analyze channels
        for channel in guild.channels:
            if isinstance(channel, discord.VoiceChannel):
                summary["voice_channels"] += 1
            elif isinstance(channel, discord.TextChannel):
                # Check if channel is public (everyone can view)
                everyone_perms = channel.overwrites_for(guild.default_role)
                if everyone_perms.view_channel is not False:
                    summary["public_channels"] += 1
                else:
                    summary["restricted_channels"] += 1
        
        return summary
    
    @staticmethod
    def format_context_for_ai(context: Dict[str, Any]) -> str:
        """Format server context into a readable string for AI processing."""
        try:
            formatted = f"SERVER: {context['server_info']['name']}\n"
            formatted += f"MEMBERS: {context['server_info']['member_count']}\n\n"
            
            # Categories and channels
            if context['categories']:
                formatted += "CATEGORIES:\n"
                for cat in context['categories']:
                    formatted += f"  {cat['name']}:\n"
                    for ch in cat['channels']:
                        formatted += f"    #{ch['name']} ({ch['type']})\n"
                formatted += "\n"
            
            # Uncategorized channels
            uncategorized = (context['channels']['text'] + 
                           context['channels']['voice'] + 
                           context['channels']['stage'])
            if uncategorized:
                formatted += "UNCATEGORIZED CHANNELS:\n"
                for ch in uncategorized:
                    formatted += f"  #{ch['name']} ({ch['type']})\n"
                formatted += "\n"
            
            # Roles
            if context['roles']:
                formatted += "ROLES:\n"
                for role in context['roles']:
                    formatted += f"  {role['name']} ({role['member_count']} members)\n"
                formatted += "\n"
            
            # Permissions summary
            perm_summary = context.get('permissions_summary', {})
            if perm_summary:
                formatted += "PERMISSIONS SUMMARY:\n"
                formatted += f"  Admin roles: {', '.join(perm_summary.get('admin_roles', []))}\n"
                formatted += f"  Moderator roles: {', '.join(perm_summary.get('moderator_roles', []))}\n"
                formatted += f"  Public channels: {perm_summary.get('public_channels', 0)}\n"
                formatted += f"  Restricted channels: {perm_summary.get('restricted_channels', 0)}\n"
                formatted += f"  Voice channels: {perm_summary.get('voice_channels', 0)}\n"
            
            return formatted
            
        except Exception as e:
            bot_logger.error(f"Error formatting context for AI: {e}", exc_info=True)
            return f"SERVER: {context.get('server_info', {}).get('name', 'Unknown')}\nError formatting context."
