"""
Contextual memory system for Discord Server Management Bot.
Stores conversation context, command history, and user preferences for enhanced interactions.
"""
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from src.utils.logger import bot_logger

@dataclass
class CommandHistory:
    """Represents a single command in history."""
    timestamp: str
    user_id: int
    user_request: str
    parsed_intents: List[str]
    actions_executed: int
    success_rate: float
    execution_time: float
    context_used: bool = False

@dataclass
class ConversationContext:
    """Represents ongoing conversation context."""
    last_command_timestamp: str
    last_actions: List[Dict[str, Any]]
    pending_clarifications: List[str]
    current_operation_state: Optional[str] = None
    referenced_channels: List[str] = None
    referenced_roles: List[str] = None
    
    def __post_init__(self):
        if self.referenced_channels is None:
            self.referenced_channels = []
        if self.referenced_roles is None:
            self.referenced_roles = []

@dataclass
class UserPreferences:
    """User-specific preferences and patterns."""
    preferred_naming_style: str = "kebab-case"  # kebab-case, snake_case, camelCase
    preferred_organization_style: str = "by_purpose"  # by_purpose, by_activity, alphabetical
    common_channel_types: List[str] = None
    common_role_patterns: List[str] = None
    timezone: Optional[str] = None
    
    def __post_init__(self):
        if self.common_channel_types is None:
            self.common_channel_types = []
        if self.common_role_patterns is None:
            self.common_role_patterns = []

class ContextualMemorySystem:
    """Manages conversation context and user preferences for enhanced bot interactions."""
    
    def __init__(self, data_file: str = "data/context_memory.json"):
        self.data_file = data_file
        self.memory_data = self._load_memory_data()
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(data_file), exist_ok=True)
    
    def _load_memory_data(self) -> Dict[str, Any]:
        """Load memory data from file."""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    bot_logger.info(f"Loaded context memory for {len(data.get('servers', {}))} servers")
                    return data
        except Exception as e:
            bot_logger.error(f"Error loading context memory: {e}")
        
        return {
            "servers": {},
            "global_stats": {
                "total_commands": 0,
                "successful_operations": 0,
                "last_updated": datetime.now().isoformat()
            }
        }
    
    def _save_memory_data(self):
        """Save memory data to file."""
        try:
            self.memory_data["global_stats"]["last_updated"] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory_data, f, indent=2, ensure_ascii=False)
            bot_logger.debug("Context memory saved successfully")
        except Exception as e:
            bot_logger.error(f"Error saving context memory: {e}")
    
    def add_command_to_history(
        self, 
        guild_id: int, 
        user_id: int, 
        user_request: str,
        parsed_response: Dict[str, Any],
        execution_results: tuple
    ):
        """Add a command to the history for context building."""
        server_key = str(guild_id)
        
        # Initialize server data if needed
        if server_key not in self.memory_data["servers"]:
            self.memory_data["servers"][server_key] = {
                "command_history": [],
                "conversation_context": {},
                "user_preferences": {}
            }
        
        # Extract execution data
        success_messages, error_messages, execution_summary = execution_results
        total_actions = len(parsed_response.get('actions', []))
        successful_actions = len(success_messages)
        success_rate = successful_actions / total_actions if total_actions > 0 else 0.0
        
        # Create command history entry
        command_entry = CommandHistory(
            timestamp=datetime.now().isoformat(),
            user_id=user_id,
            user_request=user_request,
            parsed_intents=[intent.get('description', '') for intent in parsed_response.get('intents', [])],
            actions_executed=total_actions,
            success_rate=success_rate,
            execution_time=execution_summary.get('execution_time', 0.0)
        )
        
        # Add to history (keep last 50 commands per server)
        server_data = self.memory_data["servers"][server_key]
        server_data["command_history"].append(asdict(command_entry))
        server_data["command_history"] = server_data["command_history"][-50:]
        
        # Update global stats
        self.memory_data["global_stats"]["total_commands"] += 1
        if success_rate > 0.5:
            self.memory_data["global_stats"]["successful_operations"] += 1
        
        # Update conversation context
        self._update_conversation_context(server_key, user_id, parsed_response, execution_results)
        
        # Learn user preferences
        self._learn_user_preferences(server_key, user_id, parsed_response)
        
        self._save_memory_data()
    
    def _update_conversation_context(
        self, 
        server_key: str, 
        user_id: int, 
        parsed_response: Dict[str, Any],
        execution_results: tuple
    ):
        """Update conversation context for follow-up commands."""
        user_key = str(user_id)
        server_data = self.memory_data["servers"][server_key]
        
        if "conversation_context" not in server_data:
            server_data["conversation_context"] = {}
        
        # Extract referenced items from actions
        referenced_channels = []
        referenced_roles = []
        
        for action in parsed_response.get('actions', []):
            if action.get('name'):
                if action.get('type') in ['create_channel', 'edit_channel', 'delete_channel']:
                    referenced_channels.append(action['name'])
                elif action.get('type') in ['create_role', 'delete_role']:
                    referenced_roles.append(action['name'])
            
            if action.get('category'):
                referenced_channels.append(action['category'])
        
        # Update context
        context = ConversationContext(
            last_command_timestamp=datetime.now().isoformat(),
            last_actions=parsed_response.get('actions', []),
            pending_clarifications=[],
            referenced_channels=list(set(referenced_channels)),
            referenced_roles=list(set(referenced_roles))
        )
        
        server_data["conversation_context"][user_key] = asdict(context)
    
    def _learn_user_preferences(self, server_key: str, user_id: int, parsed_response: Dict[str, Any]):
        """Learn and update user preferences from their commands."""
        user_key = str(user_id)
        server_data = self.memory_data["servers"][server_key]
        
        if "user_preferences" not in server_data:
            server_data["user_preferences"] = {}
        
        if user_key not in server_data["user_preferences"]:
            server_data["user_preferences"][user_key] = asdict(UserPreferences())
        
        preferences = server_data["user_preferences"][user_key]
        
        # Learn naming patterns
        for action in parsed_response.get('actions', []):
            name = action.get('name', '')
            if name:
                if '-' in name:
                    preferences["preferred_naming_style"] = "kebab-case"
                elif '_' in name:
                    preferences["preferred_naming_style"] = "snake_case"
                elif any(c.isupper() for c in name[1:]):
                    preferences["preferred_naming_style"] = "camelCase"
        
        # Learn organization preferences
        organization_strategy = None
        for action in parsed_response.get('actions', []):
            if action.get('organization_strategy'):
                organization_strategy = action['organization_strategy']
                break
        
        if organization_strategy:
            preferences["preferred_organization_style"] = organization_strategy
    
    def get_conversation_context(self, guild_id: int, user_id: int) -> Optional[ConversationContext]:
        """Get current conversation context for a user."""
        server_key = str(guild_id)
        user_key = str(user_id)
        
        if (server_key in self.memory_data["servers"] and 
            "conversation_context" in self.memory_data["servers"][server_key] and
            user_key in self.memory_data["servers"][server_key]["conversation_context"]):
            
            context_data = self.memory_data["servers"][server_key]["conversation_context"][user_key]
            
            # Check if context is recent (within last 30 minutes)
            last_timestamp = datetime.fromisoformat(context_data["last_command_timestamp"])
            if datetime.now() - last_timestamp < timedelta(minutes=30):
                return ConversationContext(**context_data)
        
        return None
    
    def get_user_preferences(self, guild_id: int, user_id: int) -> UserPreferences:
        """Get user preferences or defaults."""
        server_key = str(guild_id)
        user_key = str(user_id)
        
        if (server_key in self.memory_data["servers"] and 
            "user_preferences" in self.memory_data["servers"][server_key] and
            user_key in self.memory_data["servers"][server_key]["user_preferences"]):
            
            prefs_data = self.memory_data["servers"][server_key]["user_preferences"][user_key]
            return UserPreferences(**prefs_data)
        
        return UserPreferences()
    
    def get_command_history(self, guild_id: int, limit: int = 10) -> List[CommandHistory]:
        """Get recent command history for a server."""
        server_key = str(guild_id)
        
        if (server_key in self.memory_data["servers"] and 
            "command_history" in self.memory_data["servers"][server_key]):
            
            history_data = self.memory_data["servers"][server_key]["command_history"]
            recent_history = history_data[-limit:] if len(history_data) > limit else history_data
            
            return [CommandHistory(**entry) for entry in recent_history]
        
        return []
    
    def detect_follow_up_context(self, user_request: str, context: ConversationContext) -> Dict[str, Any]:
        """Detect if a request is a follow-up and provide context hints."""
        follow_up_indicators = [
            "also", "too", "as well", "additionally", "furthermore",
            "that", "there", "it", "them", "those", "this"
        ]
        
        request_lower = user_request.lower()
        is_follow_up = any(indicator in request_lower for indicator in follow_up_indicators)
        
        context_hints = {}
        
        if is_follow_up:
            # Provide context from last command
            if context.referenced_channels:
                context_hints["recent_channels"] = context.referenced_channels
            if context.referenced_roles:
                context_hints["recent_roles"] = context.referenced_roles
            if context.last_actions:
                context_hints["last_action_types"] = [action.get('type') for action in context.last_actions]
        
        return {
            "is_follow_up": is_follow_up,
            "context_hints": context_hints,
            "last_command_age_minutes": (
                datetime.now() - datetime.fromisoformat(context.last_command_timestamp)
            ).total_seconds() / 60
        }
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory system statistics."""
        total_servers = len(self.memory_data["servers"])
        total_commands = self.memory_data["global_stats"]["total_commands"]
        successful_ops = self.memory_data["global_stats"]["successful_operations"]
        
        return {
            "total_servers_tracked": total_servers,
            "total_commands_processed": total_commands,
            "successful_operations": successful_ops,
            "success_rate": successful_ops / total_commands if total_commands > 0 else 0.0,
            "last_updated": self.memory_data["global_stats"]["last_updated"]
        }
    
    def clear_old_data(self, days_old: int = 30):
        """Clear old conversation context and command history."""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        cleaned_count = 0
        
        for server_key, server_data in self.memory_data["servers"].items():
            # Clean old command history
            if "command_history" in server_data:
                original_count = len(server_data["command_history"])
                server_data["command_history"] = [
                    cmd for cmd in server_data["command_history"]
                    if datetime.fromisoformat(cmd["timestamp"]) > cutoff_date
                ]
                cleaned_count += original_count - len(server_data["command_history"])
            
            # Clean old conversation contexts
            if "conversation_context" in server_data:
                contexts_to_remove = []
                for user_key, context_data in server_data["conversation_context"].items():
                    if datetime.fromisoformat(context_data["last_command_timestamp"]) < cutoff_date:
                        contexts_to_remove.append(user_key)
                
                for user_key in contexts_to_remove:
                    del server_data["conversation_context"][user_key]
                    cleaned_count += 1
        
        if cleaned_count > 0:
            self._save_memory_data()
            bot_logger.info(f"Cleaned {cleaned_count} old memory entries")
        
        return cleaned_count
