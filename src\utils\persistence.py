"""
Persistence manager for bot data across restarts.
"""
import json
import os
import aiofiles
from typing import Dict, List, Any, Optional
from src.utils.logger import bot_logger
import config

class PersistenceManager:
    """Manages bot data persistence using JSON files."""
    
    def __init__(self, data_file: str = config.PERSISTENCE_FILE):
        self.data_file = data_file
        self.data = {}
        
    async def load_data(self) -> Dict[str, Any]:
        """Load bot data from file."""
        try:
            if os.path.exists(self.data_file):
                async with aiofiles.open(self.data_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    self.data = json.loads(content) if content.strip() else {}
                    bot_logger.info(f"Loaded bot data from {self.data_file}")
            else:
                self.data = {}
                bot_logger.info(f"No existing data file found, starting with empty data")
        except Exception as e:
            bot_logger.error(f"Error loading bot data: {e}", exc_info=True)
            self.data = {}
        
        return self.data
    
    async def save_data(self) -> bool:
        """Save bot data to file."""
        try:
            async with aiofiles.open(self.data_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(self.data, indent=2, ensure_ascii=False))
            bot_logger.debug(f"Saved bot data to {self.data_file}")
            return True
        except Exception as e:
            bot_logger.error(f"Error saving bot data: {e}", exc_info=True)
            return False
    
    async def get_setup_channels(self) -> Dict[int, int]:
        """Get all server-setup channel mappings (server_id -> channel_id)."""
        await self.load_data()
        return self.data.get('setup_channels', {})
    
    async def add_setup_channel(self, server_id: int, channel_id: int) -> bool:
        """Add a server-setup channel mapping."""
        await self.load_data()
        if 'setup_channels' not in self.data:
            self.data['setup_channels'] = {}
        
        self.data['setup_channels'][str(server_id)] = channel_id
        success = await self.save_data()
        
        if success:
            bot_logger.info(f"Added setup channel mapping: Server {server_id} -> Channel {channel_id}")
        
        return success
    
    async def remove_setup_channel(self, server_id: int) -> bool:
        """Remove a server-setup channel mapping."""
        await self.load_data()
        if 'setup_channels' in self.data and str(server_id) in self.data['setup_channels']:
            del self.data['setup_channels'][str(server_id)]
            success = await self.save_data()
            
            if success:
                bot_logger.info(f"Removed setup channel mapping for server {server_id}")
            
            return success
        
        return True  # Already removed or doesn't exist
    
    async def get_server_config(self, server_id: int) -> Dict[str, Any]:
        """Get configuration for a specific server."""
        await self.load_data()
        return self.data.get('server_configs', {}).get(str(server_id), {})
    
    async def set_server_config(self, server_id: int, config_data: Dict[str, Any]) -> bool:
        """Set configuration for a specific server."""
        await self.load_data()
        if 'server_configs' not in self.data:
            self.data['server_configs'] = {}
        
        self.data['server_configs'][str(server_id)] = config_data
        return await self.save_data()

# Global persistence manager instance
persistence_manager = PersistenceManager()
