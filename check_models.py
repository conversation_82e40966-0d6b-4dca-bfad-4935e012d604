"""
<PERSON><PERSON><PERSON> to check available Gemini models and test their performance for Discord bot tasks.
Run this script once you have a valid Gemini API key.
"""
import google.generativeai as genai
import asyncio
import time
from typing import List, Dict, Any
import config

async def list_available_models():
    """List all available Gemini models."""
    print("🔍 Checking available Gemini models...\n")
    
    try:
        genai.configure(api_key=config.GEMINI_API_KEY)
        models = genai.list_models()
        
        available_models = []
        for model in models:
            model_info = {
                'name': model.name,
                'display_name': model.display_name,
                'description': model.description,
                'input_token_limit': getattr(model, 'input_token_limit', 'Unknown'),
                'output_token_limit': getattr(model, 'output_token_limit', 'Unknown'),
                'supported_generation_methods': getattr(model, 'supported_generation_methods', [])
            }
            available_models.append(model_info)
            
            print(f"📋 Model: {model.display_name}")
            print(f"   Name: {model.name}")
            print(f"   Description: {model.description}")
            print(f"   Input Token Limit: {model_info['input_token_limit']}")
            print(f"   Output Token Limit: {model_info['output_token_limit']}")
            print(f"   Generation Methods: {model_info['supported_generation_methods']}")
            print("-" * 60)
        
        return available_models
        
    except Exception as e:
        print(f"❌ Error listing models: {e}")
        return []

async def test_model_performance(model_name: str, test_prompts: List[str]) -> Dict[str, Any]:
    """Test a specific model's performance on Discord bot tasks."""
    print(f"\n🧪 Testing model: {model_name}")
    
    try:
        model = genai.GenerativeModel(model_name)
        results = {
            'model_name': model_name,
            'test_results': [],
            'average_response_time': 0,
            'success_rate': 0,
            'total_tokens_used': 0
        }
        
        total_time = 0
        successful_tests = 0
        
        for i, prompt in enumerate(test_prompts):
            print(f"  Test {i+1}/{len(test_prompts)}: {prompt[:50]}...")
            
            start_time = time.time()
            try:
                response = model.generate_content(prompt)
                end_time = time.time()
                
                response_time = end_time - start_time
                total_time += response_time
                successful_tests += 1
                
                test_result = {
                    'prompt': prompt,
                    'response_time': response_time,
                    'response_length': len(response.text) if response.text else 0,
                    'success': True,
                    'response_preview': response.text[:100] + "..." if response.text and len(response.text) > 100 else response.text
                }
                
                print(f"    ✅ Success ({response_time:.2f}s, {test_result['response_length']} chars)")
                
            except Exception as e:
                test_result = {
                    'prompt': prompt,
                    'response_time': 0,
                    'response_length': 0,
                    'success': False,
                    'error': str(e)
                }
                print(f"    ❌ Failed: {str(e)[:50]}...")
            
            results['test_results'].append(test_result)
        
        results['average_response_time'] = total_time / len(test_prompts) if test_prompts else 0
        results['success_rate'] = (successful_tests / len(test_prompts)) * 100 if test_prompts else 0
        
        print(f"  📊 Results: {successful_tests}/{len(test_prompts)} successful, avg {results['average_response_time']:.2f}s")
        
        return results
        
    except Exception as e:
        print(f"  ❌ Model test failed: {e}")
        return {
            'model_name': model_name,
            'error': str(e),
            'success_rate': 0
        }

async def recommend_best_model():
    """Test available models and recommend the best one for Discord bot tasks."""
    print("🎯 Discord Bot Model Recommendation System\n")
    
    # Test prompts specifically designed for Discord bot tasks
    test_prompts = [
        """Convert this Discord command to JSON: "Create a support channel"
        
        Respond with JSON only:
        {
          "actions": [{"type": "create_channel", "name": "support", "channel_type": "text"}],
          "summary": "Creating support channel"
        }""",
        
        """Convert this Discord command to JSON: "Design a gaming server"
        
        Respond with JSON only:
        {
          "actions": [{"type": "apply_server_template", "template_name": "gaming_community"}],
          "summary": "Applying gaming community template"
        }""",
        
        """Convert this Discord command to JSON: "Delete all test channels"
        
        Respond with JSON only:
        {
          "actions": [{"type": "bulk_delete_channels", "pattern": "test"}],
          "summary": "Deleting channels with 'test' in name"
        }""",
        
        """Analyze this server and suggest improvements:
        Server has: general channel, random voice channel, no categories, no roles except @everyone
        
        Respond with JSON only:
        {
          "actions": [{"type": "analyze_server", "recommendations": ["Add categories", "Create roles"]}],
          "summary": "Server needs organization and role structure"
        }"""
    ]
    
    # List available models
    models = await list_available_models()
    
    if not models:
        print("❌ No models available or API key invalid")
        return
    
    # Test each model that supports text generation
    model_results = []
    for model_info in models:
        if 'generateContent' in model_info.get('supported_generation_methods', []):
            model_name = model_info['name']
            results = await test_model_performance(model_name, test_prompts)
            model_results.append(results)
    
    # Analyze results and make recommendation
    print("\n" + "="*60)
    print("📊 MODEL PERFORMANCE ANALYSIS")
    print("="*60)
    
    best_model = None
    best_score = 0
    
    for result in model_results:
        if 'error' in result:
            print(f"\n❌ {result['model_name']}: Failed to test")
            continue
        
        # Calculate composite score
        success_weight = 0.4
        speed_weight = 0.3
        consistency_weight = 0.3
        
        success_score = result['success_rate']
        speed_score = max(0, 100 - (result['average_response_time'] * 10))  # Penalize slow responses
        
        # Calculate consistency (lower variance in response times is better)
        response_times = [t['response_time'] for t in result['test_results'] if t['success']]
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            variance = sum((t - avg_time) ** 2 for t in response_times) / len(response_times)
            consistency_score = max(0, 100 - (variance * 100))
        else:
            consistency_score = 0
        
        composite_score = (success_score * success_weight + 
                          speed_score * speed_weight + 
                          consistency_score * consistency_weight)
        
        print(f"\n🤖 {result['model_name']}")
        print(f"   Success Rate: {result['success_rate']:.1f}%")
        print(f"   Avg Response Time: {result['average_response_time']:.2f}s")
        print(f"   Consistency Score: {consistency_score:.1f}")
        print(f"   Composite Score: {composite_score:.1f}")
        
        if composite_score > best_score:
            best_score = composite_score
            best_model = result
    
    # Make recommendation
    print("\n" + "="*60)
    print("🏆 RECOMMENDATION")
    print("="*60)
    
    if best_model:
        print(f"\n✅ RECOMMENDED MODEL: {best_model['model_name']}")
        print(f"   Overall Score: {best_score:.1f}/100")
        print(f"   Success Rate: {best_model['success_rate']:.1f}%")
        print(f"   Average Response Time: {best_model['average_response_time']:.2f}s")
        
        print(f"\n📝 REASONING:")
        print(f"   This model provides the best balance of:")
        print(f"   • Reliability ({best_model['success_rate']:.1f}% success rate)")
        print(f"   • Speed ({best_model['average_response_time']:.2f}s average response)")
        print(f"   • Consistency in performance")
        
        print(f"\n🔧 TO USE THIS MODEL:")
        print(f"   Update src/services/ai_service.py line 19:")
        print(f"   self.model = genai.GenerativeModel('{best_model['model_name']}')")
        
    else:
        print("❌ No suitable model found. Check API key and try again.")
    
    return best_model

# Known Gemini models (as of 2024) for reference when API is unavailable
KNOWN_GEMINI_MODELS = {
    'gemini-1.5-pro': {
        'description': 'Most capable model, best for complex reasoning',
        'input_tokens': 2000000,
        'output_tokens': 8192,
        'use_case': 'Complex analysis, detailed responses'
    },
    'gemini-1.5-flash': {
        'description': 'Fast and efficient, good balance of speed and capability',
        'input_tokens': 1000000,
        'output_tokens': 8192,
        'use_case': 'Real-time applications, quick responses'
    },
    'gemini-1.0-pro': {
        'description': 'Reliable baseline model',
        'input_tokens': 30720,
        'output_tokens': 2048,
        'use_case': 'Standard text generation tasks'
    }
}

def show_model_recommendations():
    """Show model recommendations based on Discord bot requirements."""
    print("🎯 DISCORD BOT MODEL RECOMMENDATIONS")
    print("="*50)
    
    print("\n🥇 RECOMMENDED: gemini-1.5-flash")
    print("   ✅ Fast response times (important for Discord)")
    print("   ✅ Large context window (1M tokens)")
    print("   ✅ Good balance of speed and capability")
    print("   ✅ Cost-effective for high-volume usage")
    print("   ✅ Perfect for JSON generation tasks")
    
    print("\n🥈 ALTERNATIVE: gemini-1.5-pro")
    print("   ✅ Most capable model")
    print("   ✅ Best for complex server analysis")
    print("   ⚠️ Slower response times")
    print("   ⚠️ Higher cost per request")
    print("   ✅ Excellent for detailed recommendations")
    
    print("\n🥉 FALLBACK: gemini-1.0-pro")
    print("   ✅ Reliable and stable")
    print("   ⚠️ Smaller context window")
    print("   ⚠️ Less capable than 1.5 models")
    print("   ✅ Good for basic commands")
    
    print("\n💡 FOR DISCORD BOTS:")
    print("   • Speed is crucial (users expect quick responses)")
    print("   • JSON generation reliability is essential")
    print("   • Context understanding for server analysis")
    print("   • Cost efficiency for production use")
    
    print("\n🔧 CURRENT CONFIGURATION:")
    print("   The bot is currently set to use 'gemini-1.5-flash'")
    print("   This is the optimal choice for Discord bot applications")

if __name__ == "__main__":
    print("🤖 Gemini Model Analysis for Discord Bot")
    print("="*50)
    
    try:
        # Try to run full analysis
        asyncio.run(recommend_best_model())
    except Exception as e:
        print(f"⚠️ Cannot connect to API: {e}")
        print("\nShowing offline recommendations instead:\n")
        show_model_recommendations()
