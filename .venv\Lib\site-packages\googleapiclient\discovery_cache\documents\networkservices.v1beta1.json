{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://networkservices.googleapis.com/", "batchPath": "batch", "canonicalName": "NetworkServices", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/networking", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "networkservices:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://networkservices.mtls.googleapis.com/", "name": "networkservices", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1beta1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "networkservices.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"authzExtensions": {"methods": {"create": {"description": "Creates a new `AuthzExtension` resource in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions", "httpMethod": "POST", "id": "networkservices.projects.locations.authzExtensions.create", "parameterOrder": ["parent"], "parameters": {"authzExtensionId": {"description": "Required. User-provided ID of the `AuthzExtension` resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the `AuthzExtension` resource. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/authzExtensions", "request": {"$ref": "AuthzExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified `AuthzExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions/{authzExtensionsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.authzExtensions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `AuthzExtension` resource to delete. Must be in the format `projects/{project}/locations/{location}/authzExtensions/{authz_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of the specified `AuthzExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions/{authzExtensionsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.authzExtensions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `AuthzExtension` resource to get. Must be in the format `projects/{project}/locations/{location}/authzExtensions/{authz_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzExtensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "AuthzExtension"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `AuthzExtension` resources in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions", "httpMethod": "GET", "id": "networkservices.projects.locations.authzExtensions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint about how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. The server might return fewer items than requested. If unspecified, the server picks an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results that the server returns.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the `AuthzExtension` resources are listed. These values are specified in the following format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/authzExtensions", "response": {"$ref": "ListAuthzExtensionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of the specified `AuthzExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/authzExtensions/{authzExtensionsId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.authzExtensions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Identifier. Name of the `AuthzExtension` resource in the following format: `projects/{project}/locations/{location}/authzExtensions/{authz_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authzExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Used to specify the fields to be overwritten in the `AuthzExtension` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not specify a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "AuthzExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "endpointPolicies": {"methods": {"create": {"description": "Creates a new EndpointPolicy in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies", "httpMethod": "POST", "id": "networkservices.projects.locations.endpointPolicies.create", "parameterOrder": ["parent"], "parameters": {"endpointPolicyId": {"description": "Required. Short name of the EndpointPolicy resource to be created. E.g. \"CustomECS\".", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the EndpointPolicy. Must be in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/endpointPolicies", "request": {"$ref": "EndpointPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single EndpointPolicy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.endpointPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the EndpointPolicy to delete. Must be in the format `projects/*/locations/global/endpointPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/endpointPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single EndpointPolicy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}", "httpMethod": "GET", "id": "networkservices.projects.locations.endpointPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the EndpointPolicy to get. Must be in the format `projects/*/locations/global/endpointPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/endpointPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "EndpointPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists EndpointPolicies in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies", "httpMethod": "GET", "id": "networkservices.projects.locations.endpointPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of EndpointPolicies to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListEndpointPoliciesResponse` Indicates that this is a continuation of a prior `ListEndpointPolicies` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the EndpointPolicies should be listed, specified in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests. Otherwise if one of the locations is down or unreachable, the Aggregated List request will fail.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/endpointPolicies", "response": {"$ref": "ListEndpointPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single EndpointPolicy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.endpointPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the EndpointPolicy resource. It matches pattern `projects/{project}/locations/global/endpointPolicies/{endpoint_policy}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/endpointPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the EndpointPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "EndpointPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "gateways": {"methods": {"create": {"description": "Creates a new Gateway in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/gateways", "httpMethod": "POST", "id": "networkservices.projects.locations.gateways.create", "parameterOrder": ["parent"], "parameters": {"gatewayId": {"description": "Required. Short name of the Gateway resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the Gateway. Must be in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/gateways", "request": {"$ref": "Gateway"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Gateway.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.gateways.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the Gateway to delete. Must be in the format `projects/*/locations/*/gateways/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Gateway.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}", "httpMethod": "GET", "id": "networkservices.projects.locations.gateways.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the Gateway to get. Must be in the format `projects/*/locations/*/gateways/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Gateway"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Gateways in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/gateways", "httpMethod": "GET", "id": "networkservices.projects.locations.gateways.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Gateways to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListGatewaysResponse` Indicates that this is a continuation of a prior `ListGateways` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the Gateways should be listed, specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/gateways", "response": {"$ref": "ListGatewaysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Gateway.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.gateways.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the Gateway resource. It matches pattern `projects/*/locations/*/gateways/`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the Gateway resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Gateway"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"routeViews": {"methods": {"get": {"description": "Get a single RouteView of a Gateway.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}/routeViews/{routeViewsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.gateways.routeViews.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the GatewayRouteView resource. Formats: projects/{project_number}/locations/{location}/gateways/{gateway}/routeViews/{route_view}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+/routeViews/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GatewayRouteView"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists RouteViews", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}/routeViews", "httpMethod": "GET", "id": "networkservices.projects.locations.gateways.routeViews.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of GatewayRouteViews to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListGatewayRouteViewsResponse` Indicates that this is a continuation of a prior `ListGatewayRouteViews` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Gateway to which a Route is associated. Formats: projects/{project_number}/locations/{location}/gateways/{gateway}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/routeViews", "response": {"$ref": "ListGatewayRouteViewsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "grpcRoutes": {"methods": {"create": {"description": "Creates a new GrpcRoute in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes", "httpMethod": "POST", "id": "networkservices.projects.locations.grpcRoutes.create", "parameterOrder": ["parent"], "parameters": {"grpcRouteId": {"description": "Required. Short name of the GrpcRoute resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the GrpcRoute. Must be in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/grpcRoutes", "request": {"$ref": "GrpcRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single GrpcRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.grpcRoutes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the GrpcRoute to delete. Must be in the format `projects/*/locations/global/grpcRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/grpcRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single GrpcRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}", "httpMethod": "GET", "id": "networkservices.projects.locations.grpcRoutes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the GrpcRoute to get. Must be in the format `projects/*/locations/global/grpcRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/grpcRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GrpcRoute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists GrpcRoutes in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes", "httpMethod": "GET", "id": "networkservices.projects.locations.grpcRoutes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of GrpcRoutes to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListGrpcRoutesResponse` Indicates that this is a continuation of a prior `ListGrpcRoutes` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the GrpcRoutes should be listed, specified in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests. Otherwise if one of the locations is down or unreachable, the Aggregated List request will fail.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/grpcRoutes", "response": {"$ref": "ListGrpcRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single GrpcRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.grpcRoutes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the GrpcRoute resource. It matches pattern `projects/*/locations/global/grpcRoutes/`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/grpcRoutes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the GrpcRoute resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "GrpcRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "httpRoutes": {"methods": {"create": {"description": "Creates a new HttpRoute in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes", "httpMethod": "POST", "id": "networkservices.projects.locations.httpRoutes.create", "parameterOrder": ["parent"], "parameters": {"httpRouteId": {"description": "Required. Short name of the HttpRoute resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the HttpRoute. Must be in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/httpRoutes", "request": {"$ref": "HttpRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single HttpRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.httpRoutes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the HttpRoute to delete. Must be in the format `projects/*/locations/global/httpRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/httpRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single HttpRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}", "httpMethod": "GET", "id": "networkservices.projects.locations.httpRoutes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the HttpRoute to get. Must be in the format `projects/*/locations/global/httpRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/httpRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "HttpRoute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists HttpRoute in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes", "httpMethod": "GET", "id": "networkservices.projects.locations.httpRoutes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of HttpRoutes to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListHttpRoutesResponse` Indicates that this is a continuation of a prior `ListHttpRoutes` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the HttpRoutes should be listed, specified in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests. Otherwise if one of the locations is down or unreachable, the Aggregated List request will fail.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/httpRoutes", "response": {"$ref": "ListHttpRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single HttpRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.httpRoutes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the HttpRoute resource. It matches pattern `projects/*/locations/global/httpRoutes/http_route_name>`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/httpRoutes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the HttpRoute resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "HttpRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "lbEdgeExtensions": {"methods": {"create": {"description": "Creates a new `LbEdgeExtension` resource in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions", "httpMethod": "POST", "id": "networkservices.projects.locations.lbEdgeExtensions.create", "parameterOrder": ["parent"], "parameters": {"lbEdgeExtensionId": {"description": "Required. User-provided ID of the `LbEdgeExtension` resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the `LbEdgeExtension` resource. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/lbEdgeExtensions", "request": {"$ref": "LbEdgeExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified `LbEdgeExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions/{lbEdgeExtensionsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.lbEdgeExtensions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `LbEdgeExtension` resource to delete. Must be in the format `projects/{project}/locations/{location}/lbEdgeExtensions/{lb_edge_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbEdgeExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of the specified `LbEdgeExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions/{lbEdgeExtensionsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.lbEdgeExtensions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `LbEdgeExtension` resource to get. Must be in the format `projects/{project}/locations/{location}/lbEdgeExtensions/{lb_edge_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbEdgeExtensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "LbEdgeExtension"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `LbEdgeExtension` resources in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions", "httpMethod": "GET", "id": "networkservices.projects.locations.lbEdgeExtensions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint about how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. The server might return fewer items than requested. If unspecified, the server picks an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results that the server returns.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the `LbEdgeExtension` resources are listed. These values are specified in the following format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/lbEdgeExtensions", "response": {"$ref": "ListLbEdgeExtensionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of the specified `LbEdgeExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbEdgeExtensions/{lbEdgeExtensionsId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.lbEdgeExtensions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Identifier. Name of the `LbEdgeExtension` resource in the following format: `projects/{project}/locations/{location}/lbEdgeExtensions/{lb_edge_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbEdgeExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Used to specify the fields to be overwritten in the `LbEdgeExtension` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not specify a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "LbEdgeExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "lbRouteExtensions": {"methods": {"create": {"description": "Creates a new `LbRouteExtension` resource in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions", "httpMethod": "POST", "id": "networkservices.projects.locations.lbRouteExtensions.create", "parameterOrder": ["parent"], "parameters": {"lbRouteExtensionId": {"description": "Required. User-provided ID of the `LbRouteExtension` resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the `LbRouteExtension` resource. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/lbRouteExtensions", "request": {"$ref": "LbRouteExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified `LbRouteExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.lbRouteExtensions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `LbRouteExtension` resource to delete. Must be in the format `projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbRouteExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of the specified `LbRouteExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.lbRouteExtensions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `LbRouteExtension` resource to get. Must be in the format `projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbRouteExtensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "LbRouteExtension"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `LbRouteExtension` resources in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions", "httpMethod": "GET", "id": "networkservices.projects.locations.lbRouteExtensions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint about how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. The server might return fewer items than requested. If unspecified, the server picks an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results that the server returns.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the `LbRouteExtension` resources are listed. These values are specified in the following format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/lbRouteExtensions", "response": {"$ref": "ListLbRouteExtensionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of the specified `LbRouteExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.lbRouteExtensions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Identifier. Name of the `LbRouteExtension` resource in the following format: `projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbRouteExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Used to specify the fields to be overwritten in the `LbRouteExtension` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not specify a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "LbRouteExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "lbTrafficExtensions": {"methods": {"create": {"description": "Creates a new `LbTrafficExtension` resource in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions", "httpMethod": "POST", "id": "networkservices.projects.locations.lbTrafficExtensions.create", "parameterOrder": ["parent"], "parameters": {"lbTrafficExtensionId": {"description": "Required. User-provided ID of the `LbTrafficExtension` resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the `LbTrafficExtension` resource. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/lbTrafficExtensions", "request": {"$ref": "LbTrafficExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified `LbTrafficExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.lbTrafficExtensions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `LbTrafficExtension` resource to delete. Must be in the format `projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbTrafficExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of the specified `LbTrafficExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.lbTrafficExtensions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `LbTrafficExtension` resource to get. Must be in the format `projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbTrafficExtensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "LbTrafficExtension"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `LbTrafficExtension` resources in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions", "httpMethod": "GET", "id": "networkservices.projects.locations.lbTrafficExtensions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Filtering results.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint about how to order the results.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. The server might return fewer items than requested. If unspecified, the server picks an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results that the server returns.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the `LbTrafficExtension` resources are listed. These values are specified in the following format: `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/lbTrafficExtensions", "response": {"$ref": "ListLbTrafficExtensionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of the specified `LbTrafficExtension` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.lbTrafficExtensions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Identifier. Name of the `LbTrafficExtension` resource in the following format: `projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/lbTrafficExtensions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server can ignore the request if it has already been completed. The server guarantees that for 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server ignores the second request This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Used to specify the fields to be overwritten in the `LbTrafficExtension` resource by the update. The fields specified in the `update_mask` are relative to the resource, not the full request. A field is overwritten if it is in the mask. If the user does not specify a mask, then all fields are overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "LbTrafficExtension"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "meshes": {"methods": {"create": {"description": "Creates a new Mesh in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/meshes", "httpMethod": "POST", "id": "networkservices.projects.locations.meshes.create", "parameterOrder": ["parent"], "parameters": {"meshId": {"description": "Required. Short name of the Mesh resource to be created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the Mesh. Must be in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/meshes", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Mesh.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.meshes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the Mesh to delete. Must be in the format `projects/*/locations/global/meshes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/meshes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Mesh.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}", "httpMethod": "GET", "id": "networkservices.projects.locations.meshes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the Mesh to get. Must be in the format `projects/*/locations/global/meshes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/meshes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Meshes in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/meshes", "httpMethod": "GET", "id": "networkservices.projects.locations.meshes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of Meshes to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListMeshesResponse` Indicates that this is a continuation of a prior `ListMeshes` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the Meshes should be listed, specified in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests. Otherwise if one of the locations is down or unreachable, the Aggregated List request will fail.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/meshes", "response": {"$ref": "ListMeshesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Mesh.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.meshes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the Mesh resource. It matches pattern `projects/*/locations/global/meshes/`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/meshes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the Mesh resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"routeViews": {"methods": {"get": {"description": "Get a single RouteView of a Mesh.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}/routeViews/{routeViewsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.meshes.routeViews.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the MeshRouteView resource. Format: projects/{project_number}/locations/{location}/meshes/{mesh}/routeViews/{route_view}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/meshes/[^/]+/routeViews/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "MeshRouteView"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists RouteViews", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}/routeViews", "httpMethod": "GET", "id": "networkservices.projects.locations.meshes.routeViews.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of MeshRouteViews to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListMeshRouteViewsResponse` Indicates that this is a continuation of a prior `ListMeshRouteViews` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Mesh to which a Route is associated. Format: projects/{project_number}/locations/{location}/meshes/{mesh}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/meshes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/routeViews", "response": {"$ref": "ListMeshRouteViewsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "networkservices.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "networkservices.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceBindings": {"methods": {"create": {"description": "Creates a new ServiceBinding in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings", "httpMethod": "POST", "id": "networkservices.projects.locations.serviceBindings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the ServiceBinding. Must be in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "serviceBindingId": {"description": "Required. Short name of the ServiceBinding resource to be created.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/serviceBindings", "request": {"$ref": "ServiceBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ServiceBinding.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.serviceBindings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ServiceBinding to delete. Must be in the format `projects/*/locations/*/serviceBindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ServiceBinding.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.serviceBindings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ServiceBinding to get. Must be in the format `projects/*/locations/*/serviceBindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceBindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "ServiceBinding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ServiceBinding in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings", "httpMethod": "GET", "id": "networkservices.projects.locations.serviceBindings.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of ServiceBindings to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListServiceBindingsResponse` Indicates that this is a continuation of a prior `ListRouters` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the ServiceBindings should be listed, specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/serviceBindings", "response": {"$ref": "ListServiceBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ServiceBinding.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.serviceBindings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the ServiceBinding resource. It matches pattern `projects/*/locations/*/serviceBindings/`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceBindings/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the ServiceBinding resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "ServiceBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "serviceLbPolicies": {"methods": {"create": {"description": "Creates a new ServiceLbPolicy in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies", "httpMethod": "POST", "id": "networkservices.projects.locations.serviceLbPolicies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the ServiceLbPolicy. Must be in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "serviceLbPolicyId": {"description": "Required. Short name of the ServiceLbPolicy resource to be created. E.g. for resource name `projects/{project}/locations/{location}/serviceLbPolicies/{service_lb_policy_name}`. the id is value of {service_lb_policy_name}", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/serviceLbPolicies", "request": {"$ref": "ServiceLbPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ServiceLbPolicy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.serviceLbPolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ServiceLbPolicy to delete. Must be in the format `projects/{project}/locations/{location}/serviceLbPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceLbPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ServiceLbPolicy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}", "httpMethod": "GET", "id": "networkservices.projects.locations.serviceLbPolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the ServiceLbPolicy to get. Must be in the format `projects/{project}/locations/{location}/serviceLbPolicies/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceLbPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "ServiceLbPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ServiceLbPolicies in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies", "httpMethod": "GET", "id": "networkservices.projects.locations.serviceLbPolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of ServiceLbPolicies to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListServiceLbPoliciesResponse` Indicates that this is a continuation of a prior `ListRouters` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the ServiceLbPolicies should be listed, specified in the format `projects/{project}/locations/{location}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/serviceLbPolicies", "response": {"$ref": "ListServiceLbPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ServiceLbPolicy.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.serviceLbPolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the ServiceLbPolicy resource. It matches pattern `projects/{project}/locations/{location}/serviceLbPolicies/{service_lb_policy_name}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/serviceLbPolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the ServiceLbPolicy resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "ServiceLbPolicy"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "tcpRoutes": {"methods": {"create": {"description": "Creates a new TcpRoute in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes", "httpMethod": "POST", "id": "networkservices.projects.locations.tcpRoutes.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the TcpRoute. Must be in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "tcpRouteId": {"description": "Required. Short name of the TcpRoute resource to be created.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/tcpRoutes", "request": {"$ref": "TcpRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single TcpRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.tcpRoutes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the TcpRoute to delete. Must be in the format `projects/*/locations/global/tcpRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tcpRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single TcpRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}", "httpMethod": "GET", "id": "networkservices.projects.locations.tcpRoutes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the TcpRoute to get. Must be in the format `projects/*/locations/global/tcpRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tcpRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "TcpRoute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists TcpRoute in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes", "httpMethod": "GET", "id": "networkservices.projects.locations.tcpRoutes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of TcpRoutes to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListTcpRoutesResponse` Indicates that this is a continuation of a prior `ListTcpRoutes` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the TcpRoutes should be listed, specified in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests. Otherwise if one of the locations is down or unreachable, the Aggregated List request will fail.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/tcpRoutes", "response": {"$ref": "ListTcpRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single TcpRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.tcpRoutes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the TcpRoute resource. It matches pattern `projects/*/locations/global/tcpRoutes/tcp_route_name>`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tcpRoutes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the TcpRoute resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "TcpRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "tlsRoutes": {"methods": {"create": {"description": "Creates a new TlsRoute in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes", "httpMethod": "POST", "id": "networkservices.projects.locations.tlsRoutes.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the TlsRoute. Must be in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "tlsRouteId": {"description": "Required. Short name of the TlsRoute resource to be created.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/tlsRoutes", "request": {"$ref": "TlsRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single TlsRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.tlsRoutes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the TlsRoute to delete. Must be in the format `projects/*/locations/global/tlsRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tlsRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single TlsRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}", "httpMethod": "GET", "id": "networkservices.projects.locations.tlsRoutes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the TlsRoute to get. Must be in the format `projects/*/locations/global/tlsRoutes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tlsRoutes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "TlsRoute"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists TlsRoute in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes", "httpMethod": "GET", "id": "networkservices.projects.locations.tlsRoutes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of TlsRoutes to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListTlsRoutesResponse` Indicates that this is a continuation of a prior `ListTlsRoutes` call, and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the TlsRoutes should be listed, specified in the format `projects/*/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If true, allow partial responses for multi-regional Aggregated List requests. Otherwise if one of the locations is down or unreachable, the Aggregated List request will fail.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/tlsRoutes", "response": {"$ref": "ListTlsRoutesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single TlsRoute.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.tlsRoutes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the TlsRoute resource. It matches pattern `projects/*/locations/global/tlsRoutes/tls_route_name>`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/tlsRoutes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the TlsRoute resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "TlsRoute"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "wasmPlugins": {"methods": {"create": {"description": "Creates a new `WasmPlugin` resource in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins", "httpMethod": "POST", "id": "networkservices.projects.locations.wasmPlugins.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the `WasmPlugin` resource. Must be in the format `projects/{project}/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "wasmPluginId": {"description": "Required. User-provided ID of the `WasmPlugin` resource to be created.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/wasmPlugins", "request": {"$ref": "Wasm<PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified `WasmPlugin` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.wasmPlugins.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `WasmPlugin` resource to delete. Must be in the format `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/wasmPlugins/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of the specified `WasmPlugin` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.wasmPlugins.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `WasmPlugin` resource to get. Must be in the format `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/wasmPlugins/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Determines how much data must be returned in the response. See [AIP-157](https://google.aip.dev/157).", "enum": ["WASM_PLUGIN_VIEW_UNSPECIFIED", "WASM_PLUGIN_VIEW_BASIC", "WASM_PLUGIN_VIEW_FULL"], "enumDescriptions": ["Unspecified value. Do not use.", "If specified in the `GET` request for a `WasmPlugin` resource, the server's response includes just the `WasmPlugin` resource.", "If specified in the `GET` request for a `WasmPlugin` resource, the server's response includes the `WasmPlugin` resource with all its versions."], "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Wasm<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `WasmPlugin` resources in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins", "httpMethod": "GET", "id": "networkservices.projects.locations.wasmPlugins.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of `WasmPlugin` resources to return per call. If not specified, at most 50 `WasmPlugin` resources are returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListWasmPluginsResponse` call. Indicates that this is a continuation of a prior `ListWasmPlugins` call, and that the next page of data is to be returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the `WasmPlugin` resources are listed, specified in the following format: `projects/{project}/locations/global`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/wasmPlugins", "response": {"$ref": "ListWasmPluginsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of the specified `WasmPlugin` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}", "httpMethod": "PATCH", "id": "networkservices.projects.locations.wasmPlugins.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the `WasmPlugin` resource in the following format: `projects/{project}/locations/{location}/wasmPlugins/{wasm_plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/wasmPlugins/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. Used to specify the fields to be overwritten in the `WasmPlugin` resource by the update. The fields specified in the `update_mask` field are relative to the resource, not the full request. An omitted `update_mask` field is treated as an implied `update_mask` field equivalent to all fields that are populated (that have a non-empty value). The `update_mask` field supports a special value `*`, which means that each field in the given `WasmPlugin` resource (including the empty ones) replaces the current value.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Wasm<PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"versions": {"methods": {"create": {"description": "Creates a new `WasmPluginVersion` resource in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions", "httpMethod": "POST", "id": "networkservices.projects.locations.wasmPlugins.versions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the `WasmPluginVersion` resource. Must be in the format `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/wasmPlugins/[^/]+$", "required": true, "type": "string"}, "wasmPluginVersionId": {"description": "Required. User-provided ID of the `WasmPluginVersion` resource to be created.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/versions", "request": {"$ref": "WasmPluginVersion"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified `WasmPluginVersion` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions/{versionsId}", "httpMethod": "DELETE", "id": "networkservices.projects.locations.wasmPlugins.versions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `WasmPluginVersion` resource to delete. Must be in the format `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}/versions/{wasm_plugin_version}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/wasmPlugins/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of the specified `WasmPluginVersion` resource.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions/{versionsId}", "httpMethod": "GET", "id": "networkservices.projects.locations.wasmPlugins.versions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. A name of the `WasmPluginVersion` resource to get. Must be in the format `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}/versions/{wasm_plugin_version}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/wasmPlugins/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "WasmPluginVersion"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists `WasmPluginVersion` resources in a given project and location.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions", "httpMethod": "GET", "id": "networkservices.projects.locations.wasmPlugins.versions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of `WasmPluginVersion` resources to return per call. If not specified, at most 50 `WasmPluginVersion` resources are returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListWasmPluginVersionsResponse` call. Indicates that this is a continuation of a prior `ListWasmPluginVersions` call, and that the next page of data is to be returned.", "location": "query", "type": "string"}, "parent": {"description": "Required. The `WasmPlugin` resource whose `WasmPluginVersion`s are listed, specified in the following format: `projects/{project}/locations/global/wasmPlugins/{wasm_plugin}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/wasmPlugins/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/versions", "response": {"$ref": "ListWasmPluginVersionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250626", "rootUrl": "https://networkservices.googleapis.com/", "schemas": {"AuthzExtension": {"description": "`AuthzExtension` is a resource that allows traffic forwarding to a callout backend service to make an authorization decision.", "id": "AuthzExtension", "properties": {"authority": {"description": "Required. The `:authority` header in the gRPC request sent from Envoy to the extension service.", "type": "string"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "failOpen": {"description": "Optional. Determines how the proxy behaves if the call to the extension fails or times out. When set to `TRUE`, request or response processing continues without error. Any subsequent extensions in the extension chain are also executed. When set to `FALSE` or the default setting of `FALSE` is used, one of the following happens: * If response headers have not been delivered to the downstream client, a generic 500 error is returned to the client. The error response can be tailored by configuring a custom error response in the load balancer. * If response headers have been delivered, then the HTTP stream to the downstream client is reset.", "type": "boolean"}, "forwardHeaders": {"description": "Optional. List of the HTTP headers to forward to the extension (from the client). If omitted, all headers are sent. Each element is a string indicating the header name.", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `AuthzExtension` resource. The format must comply with [the requirements for labels](/compute/docs/labeling-resources#requirements) for Google Cloud resources.", "type": "object"}, "loadBalancingScheme": {"description": "Required. All backend services and forwarding rules referenced by this extension must share the same load balancing scheme. Supported values: `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more information, refer to [Backend services overview](https://cloud.google.com/load-balancing/docs/backend-service).", "enum": ["LOAD_BALANCING_SCHEME_UNSPECIFIED", "INTERNAL_MANAGED", "EXTERNAL_MANAGED"], "enumDescriptions": ["Default value. Do not use.", "Signifies that this is used for Internal HTTP(S) Load Balancing.", "Signifies that this is used for External Managed HTTP(S) Load Balancing."], "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. The metadata provided here is included as part of the `metadata_context` (of type `google.protobuf.Struct`) in the `ProcessingRequest` message sent to the extension server. The metadata is available under the namespace `com.google.authz_extension.`. The following variables are supported in the metadata Struct: `{forwarding_rule_id}` - substituted with the forwarding rule's fully qualified resource name.", "type": "object"}, "name": {"description": "Required. Identifier. Name of the `AuthzExtension` resource in the following format: `projects/{project}/locations/{location}/authzExtensions/{authz_extension}`.", "type": "string"}, "service": {"description": "Required. The reference to the service that runs the extension. To configure a callout extension, `service` must be a fully-qualified reference to a [backend service](https://cloud.google.com/compute/docs/reference/rest/v1/backendServices) in the format: `https://www.googleapis.com/compute/v1/projects/{project}/regions/{region}/backendServices/{backendService}` or `https://www.googleapis.com/compute/v1/projects/{project}/global/backendServices/{backendService}`.", "type": "string"}, "timeout": {"description": "Required. Specifies the timeout for each individual message on the stream. The timeout must be between 10-10000 milliseconds.", "format": "google-duration", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "wireFormat": {"description": "Optional. The format of communication supported by the callout extension. If not specified, the default value `EXT_PROC_GRPC` is used.", "enum": ["WIRE_FORMAT_UNSPECIFIED", "EXT_PROC_GRPC"], "enumDescriptions": ["Not specified.", "The extension service uses ext_proc gRPC API over a gRPC stream. This is the default value if the wire format is not specified. The backend service for the extension must use HTTP2 or H2C as the protocol. All `supported_events` for a client request are sent as part of the same gRPC stream."], "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EndpointMatcher": {"description": "A definition of a matcher that selects endpoints to which the policies should be applied.", "id": "EndpointMatcher", "properties": {"metadataLabelMatcher": {"$ref": "MetadataLabelMatcher", "description": "The matcher is based on node metadata presented by xDS clients."}}, "type": "object"}, "EndpointPolicy": {"description": "EndpointPolicy is a resource that helps apply desired configuration on the endpoints that match specific criteria. For example, this resource can be used to apply \"authentication config\" an all endpoints that serve on port 8080.", "id": "EndpointPolicy", "properties": {"authorizationPolicy": {"description": "Optional. This field specifies the URL of AuthorizationPolicy resource that applies authorization policies to the inbound traffic at the matched endpoints. Refer to Authorization. If this field is not specified, authorization is disabled(no authz checks) for this endpoint.", "type": "string"}, "clientTlsPolicy": {"description": "Optional. A URL referring to a ClientTlsPolicy resource. ClientTlsPolicy can be set to specify the authentication for traffic from the proxy to the actual endpoints. More specifically, it is applied to the outgoing traffic from the proxy to the endpoint. This is typically used for sidecar model where the proxy identifies itself as endpoint to the control plane, with the connection between sidecar and endpoint requiring authentication. If this field is not set, authentication is disabled(open). Applicable only when EndpointPolicyType is SIDECAR_PROXY.", "type": "string"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "endpointMatcher": {"$ref": "EndpointMatcher", "description": "Required. A matcher that selects endpoints to which the policies should be applied."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the EndpointPolicy resource.", "type": "object"}, "name": {"description": "Identifier. Name of the EndpointPolicy resource. It matches pattern `projects/{project}/locations/global/endpointPolicies/{endpoint_policy}`.", "type": "string"}, "securityPolicy": {"description": "Optional. A URL referring to a SecurityPolicy resource. SecurityPolicy is used to enforce rate limiting policy on the inbound traffic at the identified backends. If this field is not set, rate limiting is disabled for this endpoint.", "type": "string"}, "serverTlsPolicy": {"description": "Optional. A URL referring to ServerTlsPolicy resource. ServerTlsPolicy is used to determine the authentication policy to be applied to terminate the inbound traffic at the identified backends. If this field is not set, authentication is disabled(open) for this endpoint.", "type": "string"}, "trafficPortSelector": {"$ref": "TrafficPortSelector", "description": "Optional. Port selector for the (matched) endpoints. If no port selector is provided, the matched config is applied to all ports."}, "type": {"description": "Required. The type of endpoint policy. This is primarily used to validate the configuration.", "enum": ["ENDPOINT_POLICY_TYPE_UNSPECIFIED", "SIDECAR_PROXY", "GRPC_SERVER"], "enumDescriptions": ["Default value. Must not be used.", "Represents a proxy deployed as a sidecar.", "Represents a proxyless gRPC backend."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ExtensionChain": {"description": "A single extension chain wrapper that contains the match conditions and extensions to execute.", "id": "ExtensionChain", "properties": {"extensions": {"description": "Required. A set of extensions to execute for the matching request. At least one extension is required. Up to 3 extensions can be defined for each extension chain for `LbTrafficExtension` resource. `LbRouteExtension` and `LbEdgeExtension` chains are limited to 1 extension per extension chain.", "items": {"$ref": "ExtensionChainExtension"}, "type": "array"}, "matchCondition": {"$ref": "ExtensionChainMatchCondition", "description": "Required. Conditions under which this chain is invoked for a request."}, "name": {"description": "Required. The name for this extension chain. The name is logged as part of the HTTP request logs. The name must conform with RFC-1034, is restricted to lower-cased letters, numbers and hyphens, and can have a maximum length of 63 characters. Additionally, the first character must be a letter and the last a letter or a number.", "type": "string"}}, "type": "object"}, "ExtensionChainExtension": {"description": "A single extension in the chain to execute for the matching request.", "id": "ExtensionChainExtension", "properties": {"allowDynamicForwarding": {"description": "Optional. When set to `TRUE`, the response from an extension service is allowed to set the `com.google.envoy.dynamic_forwarding` namespace in the dynamic metadata. This field is not supported for plugin extensions. Setting it results in a validation error.", "type": "boolean"}, "authority": {"description": "Optional. The `:authority` header in the gRPC request sent from Envoy to the extension service. Required for Callout extensions. This field is not supported for plugin extensions. Setting it results in a validation error.", "type": "string"}, "failOpen": {"description": "Optional. Determines how the proxy behaves if the call to the extension fails or times out. When set to `TRUE`, request or response processing continues without error. Any subsequent extensions in the extension chain are also executed. When set to `FALSE` or the default setting of `FALSE` is used, one of the following happens: * If response headers have not been delivered to the downstream client, a generic 500 error is returned to the client. The error response can be tailored by configuring a custom error response in the load balancer. * If response headers have been delivered, then the HTTP stream to the downstream client is reset.", "type": "boolean"}, "forwardHeaders": {"description": "Optional. List of the HTTP headers to forward to the extension (from the client or backend). If omitted, all headers are sent. Each element is a string indicating the header name.", "items": {"type": "string"}, "type": "array"}, "metadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. The metadata provided here is included as part of the `metadata_context` (of type `google.protobuf.Struct`) in the `ProcessingRequest` message sent to the extension server. The metadata is available under the namespace `com.google....`. For example: `com.google.lb_traffic_extension.lbtrafficextension1.chain1.ext1`. The following variables are supported in the metadata: `{forwarding_rule_id}` - substituted with the forwarding rule's fully qualified resource name. This field must not be set for plugin extensions. Setting it results in a validation error. You can set metadata at either the resource level or the extension level. The extension level metadata is recommended because you can pass a different set of metadata through each extension to the backend. This field is subject to following limitations: * The total size of the metadata must be less than 1KiB. * The total number of keys in the metadata must be less than 16. * The length of each key must be less than 64 characters. * The length of each value must be less than 1024 characters. * All values must be strings.", "type": "object"}, "name": {"description": "Required. The name for this extension. The name is logged as part of the HTTP request logs. The name must conform with RFC-1034, is restricted to lower-cased letters, numbers and hyphens, and can have a maximum length of 63 characters. Additionally, the first character must be a letter and the last a letter or a number.", "type": "string"}, "requestBodySendMode": {"description": "Optional. Configures the send mode for request body processing. The field can only be set if `supported_events` includes `REQUEST_BODY`. If `supported_events` includes `REQUEST_BODY`, but `request_body_send_mode` is unset, the default value `STREAMED` is used. When this field is set to `FULL_DUPLEX_STREAMED`, `supported_events` must include both `REQUEST_BODY` and `REQUEST_TRAILERS`. This field can be set only for `LbTrafficExtension` and `LbRouteExtension` resources, and only when the `service` field of the extension points to a `BackendService`. Only `FULL_DUPLEX_STREAMED` mode is supported for `LbRouteExtension` resources.", "enum": ["BODY_SEND_MODE_UNSPECIFIED", "BODY_SEND_MODE_STREAMED", "BODY_SEND_MODE_FULL_DUPLEX_STREAMED"], "enumDescriptions": ["Default value. Do not use.", "Calls to the extension are executed in the streamed mode. Subsequent chunks will be sent only after the previous chunks have been processed. The content of the body chunks is sent one way to the extension. Extension may send modified chunks back. This is the default value if the processing mode is not specified.", "Calls are executed in the full duplex mode. Subsequent chunks will be sent for processing without waiting for the response for the previous chunk or for the response for `REQUEST_HEADERS` event. Extension can freely modify or chunk the body contents. If the extension doesn't send the body contents back, the next extension in the chain or the upstream will receive an empty body."], "type": "string"}, "responseBodySendMode": {"description": "Optional. Configures the send mode for response processing. If unspecified, the default value `STREAMED` is used. When this field is set to `FULL_DUPLEX_STREAMED`, `supported_events` must include both `RESPONSE_BODY` and `RESPONSE_TRAILERS`. This field can be set only for `LbTrafficExtension` resources, and only when the `service` field of the extension points to a `BackendService`.", "enum": ["BODY_SEND_MODE_UNSPECIFIED", "BODY_SEND_MODE_STREAMED", "BODY_SEND_MODE_FULL_DUPLEX_STREAMED"], "enumDescriptions": ["Default value. Do not use.", "Calls to the extension are executed in the streamed mode. Subsequent chunks will be sent only after the previous chunks have been processed. The content of the body chunks is sent one way to the extension. Extension may send modified chunks back. This is the default value if the processing mode is not specified.", "Calls are executed in the full duplex mode. Subsequent chunks will be sent for processing without waiting for the response for the previous chunk or for the response for `REQUEST_HEADERS` event. Extension can freely modify or chunk the body contents. If the extension doesn't send the body contents back, the next extension in the chain or the upstream will receive an empty body."], "type": "string"}, "service": {"description": "Required. The reference to the service that runs the extension. To configure a callout extension, `service` must be a fully-qualified reference to a [backend service](https://cloud.google.com/compute/docs/reference/rest/v1/backendServices) in the format: `https://www.googleapis.com/compute/v1/projects/{project}/regions/{region}/backendServices/{backendService}` or `https://www.googleapis.com/compute/v1/projects/{project}/global/backendServices/{backendService}`. To configure a plugin extension, `service` must be a reference to a [`WasmPlugin` resource](https://cloud.google.com/service-extensions/docs/reference/rest/v1beta1/projects.locations.wasmPlugins) in the format: `projects/{project}/locations/{location}/wasmPlugins/{plugin}` or `//networkservices.googleapis.com/projects/{project}/locations/{location}/wasmPlugins/{wasmPlugin}`. Plugin extensions are currently supported for the `LbTrafficExtension`, the `LbRouteExtension`, and the `LbEdgeExtension` resources.", "type": "string"}, "supportedEvents": {"description": "Optional. A set of events during request or response processing for which this extension is called. For the `LbTrafficExtension` resource, this field is required. For the `LbRouteExtension` resource, this field is optional. If unspecified, `REQUEST_HEADERS` event is assumed as supported. For the `LbEdgeExtension` resource, this field is required and must only contain `REQUEST_HEADERS` event.", "items": {"enum": ["EVENT_TYPE_UNSPECIFIED", "REQUEST_HEADERS", "REQUEST_BODY", "RESPONSE_HEADERS", "RESPONSE_BODY", "REQUEST_TRAILERS", "RESPONSE_TRAILERS"], "enumDescriptions": ["Unspecified value. Do not use.", "If included in `supported_events`, the extension is called when the HTTP request headers arrive.", "If included in `supported_events`, the extension is called when the HTTP request body arrives.", "If included in `supported_events`, the extension is called when the HTTP response headers arrive.", "If included in `supported_events`, the extension is called when the HTTP response body arrives.", "If included in `supported_events`, the extension is called when the HTTP request trailers arrives.", "If included in `supported_events`, the extension is called when the HTTP response trailers arrives."], "type": "string"}, "type": "array"}, "timeout": {"description": "Optional. Specifies the timeout for each individual message on the stream. The timeout must be between `10`-`10000` milliseconds. Required for callout extensions. This field is not supported for plugin extensions. Setting it results in a validation error.", "format": "google-duration", "type": "string"}}, "type": "object"}, "ExtensionChainMatchCondition": {"description": "Conditions under which this chain is invoked for a request.", "id": "ExtensionChainMatchCondition", "properties": {"celExpression": {"description": "Required. A Common Expression Language (CEL) expression that is used to match requests for which the extension chain is executed. For more information, see [CEL matcher language reference](https://cloud.google.com/service-extensions/docs/cel-matcher-language-reference).", "type": "string"}}, "type": "object"}, "Gateway": {"description": "Gateway represents the configuration for a proxy, typically a load balancer. It captures the ip:port over which the services are exposed by the proxy, along with any policy configurations. Routes have reference to to Gateways to dictate how requests should be routed by this Gateway.", "id": "Gateway", "properties": {"addresses": {"description": "Optional. Zero or one IPv4 or IPv6 address on which the Gateway will receive the traffic. When no address is provided, an IP from the subnetwork is allocated This field only applies to gateways of type 'SECURE_WEB_GATEWAY'. Gateways of type 'OPEN_MESH' listen on 0.0.0.0 for IPv4 and :: for IPv6.", "items": {"type": "string"}, "type": "array"}, "certificateUrls": {"description": "Optional. A fully-qualified Certificates URL reference. The proxy presents a Certificate (selected based on SNI) when establishing a TLS connection. This feature only applies to gateways of type 'SECURE_WEB_GATEWAY'.", "items": {"type": "string"}, "type": "array"}, "createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "envoyHeaders": {"description": "Optional. Determines if envoy will insert internal debug headers into upstream requests. Other Envoy headers may still be injected. By default, envoy will not insert any debug headers.", "enum": ["ENVOY_HEADERS_UNSPECIFIED", "NONE", "DEBUG_HEADERS"], "enumDescriptions": ["Defaults to NONE.", "Suppress envoy debug headers.", "Envoy will insert default internal debug headers into upstream requests: x-envoy-attempt-count x-envoy-is-timeout-retry x-envoy-expected-rq-timeout-ms x-envoy-original-path x-envoy-upstream-stream-duration-ms"], "type": "string"}, "gatewaySecurityPolicy": {"description": "Optional. A fully-qualified GatewaySecurityPolicy URL reference. Defines how a server should apply security policy to inbound (VM to Proxy) initiated connections. For example: `projects/*/locations/*/gatewaySecurityPolicies/swg-policy`. This policy is specific to gateways of type 'SECURE_WEB_GATEWAY'.", "type": "string"}, "ipVersion": {"description": "Optional. The IP Version that will be used by this gateway. Valid options are IPV4 or IPV6. Default is IPV4.", "enum": ["IP_VERSION_UNSPECIFIED", "IPV4", "IPV6"], "enumDescriptions": ["The type when IP version is not specified. Defaults to IPV4.", "The type for IP version 4.", "The type for IP version 6."], "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the Gateway resource.", "type": "object"}, "name": {"description": "Identifier. Name of the Gateway resource. It matches pattern `projects/*/locations/*/gateways/`.", "type": "string"}, "network": {"description": "Optional. The relative resource name identifying the VPC network that is using this configuration. For example: `projects/*/global/networks/network-1`. Currently, this field is specific to gateways of type 'SECURE_WEB_GATEWAY'.", "type": "string"}, "ports": {"description": "Required. One or more port numbers (1-65535), on which the Gateway will receive traffic. The proxy binds to the specified ports. Gateways of type 'SECURE_WEB_GATEWAY' are limited to 1 port. Gateways of type 'OPEN_MESH' listen on 0.0.0.0 for IPv4 and :: for IPv6 and support multiple ports.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "routingMode": {"description": "Optional. The routing mode of the Gateway. This field is configurable only for gateways of type SECURE_WEB_GATEWAY. This field is required for gateways of type SECURE_WEB_GATEWAY.", "enum": ["EXPLICIT_ROUTING_MODE", "NEXT_HOP_ROUTING_MODE"], "enumDescriptions": ["The routing mode is explicit; clients are configured to send traffic through the gateway. This is the default routing mode.", "The routing mode is next-hop. Clients are unaware of the gateway, and a route (advanced route or other route type) can be configured to direct traffic from client to gateway. The gateway then acts as a next-hop to the destination."], "type": "string"}, "scope": {"description": "Optional. <PERSON><PERSON> determines how configuration across multiple Gateway instances are merged. The configuration for multiple Gateway instances with the same scope will be merged as presented as a single configuration to the proxy/load balancer. Max length 64 characters. <PERSON><PERSON> should start with a letter and can only have letters, numbers, hyphens.", "type": "string"}, "selfLink": {"description": "Output only. Server-defined URL of this resource", "readOnly": true, "type": "string"}, "serverTlsPolicy": {"description": "Optional. A fully-qualified ServerTLSPolicy URL reference. Specifies how TLS traffic is terminated. If empty, TLS termination is disabled.", "type": "string"}, "subnetwork": {"description": "Optional. The relative resource name identifying the subnetwork in which this SWG is allocated. For example: `projects/*/regions/us-central1/subnetworks/network-1` Currently, this field is specific to gateways of type 'SECURE_WEB_GATEWAY\".", "type": "string"}, "type": {"description": "Immutable. The type of the customer managed gateway. This field is required. If unspecified, an error is returned.", "enum": ["TYPE_UNSPECIFIED", "OPEN_MESH", "SECURE_WEB_GATEWAY"], "enumDescriptions": ["The type of the customer managed gateway is unspecified.", "The type of the customer managed gateway is TrafficDirector Open Mesh.", "The type of the customer managed gateway is SecureWebGateway (SWG)."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GatewayRouteView": {"description": "GatewayRouteView defines view-only resource for Routes to a Gateway", "id": "GatewayRouteView", "properties": {"name": {"description": "Output only. Identifier. Full path name of the GatewayRouteView resource. Format: projects/{project_number}/locations/{location}/gateways/{gateway}/routeViews/{route_view}", "readOnly": true, "type": "string"}, "routeId": {"description": "Output only. The resource id for the route.", "readOnly": true, "type": "string"}, "routeLocation": {"description": "Output only. Location where the route exists.", "readOnly": true, "type": "string"}, "routeProjectNumber": {"description": "Output only. Project number where the route exists.", "format": "int64", "readOnly": true, "type": "string"}, "routeType": {"description": "Output only. Type of the route: HttpRoute,GrpcRoute,TcpRoute, or TlsRoute", "readOnly": true, "type": "string"}}, "type": "object"}, "GrpcRoute": {"description": "GrpcRoute is the resource defining how gRPC traffic routed by a Mesh or Gateway resource is routed.", "id": "GrpcRoute", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "gateways": {"description": "Optional. Gateways defines a list of gateways this GrpcRoute is attached to, as one of the routing rules to route the requests served by the gateway. Each gateway reference should match the pattern: `projects/*/locations/global/gateways/`", "items": {"type": "string"}, "type": "array"}, "hostnames": {"description": "Required. Service hostnames with an optional port for which this route describes traffic. Format: [:] Hostname is the fully qualified domain name of a network host. This matches the RFC 1123 definition of a hostname with 2 notable exceptions: - IPs are not allowed. - A hostname may be prefixed with a wildcard label (`*.`). The wildcard label must appear by itself as the first label. Hostname can be \"precise\" which is a domain name without the terminating dot of a network host (e.g. `foo.example.com`) or \"wildcard\", which is a domain name prefixed with a single wildcard label (e.g. `*.example.com`). Note that as per RFC1035 and RFC1123, a label must consist of lower case alphanumeric characters or '-', and must start and end with an alphanumeric character. No other punctuation is allowed. The routes associated with a Mesh or Gateway must have unique hostnames. If you attempt to attach multiple routes with conflicting hostnames, the configuration will be rejected. For example, while it is acceptable for routes for the hostnames `*.foo.bar.com` and `*.bar.com` to be associated with the same route, it is not possible to associate two routes both with `*.bar.com` or both with `bar.com`. If a port is specified, then gRPC clients must use the channel URI with the port to match this rule (i.e. \"xds:///service:123\"), otherwise they must supply the URI without a port (i.e. \"xds:///service\").", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the GrpcRoute resource.", "type": "object"}, "meshes": {"description": "Optional. Meshes defines a list of meshes this GrpcRoute is attached to, as one of the routing rules to route the requests served by the mesh. Each mesh reference should match the pattern: `projects/*/locations/global/meshes/`", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Identifier. Name of the GrpcRoute resource. It matches pattern `projects/*/locations/global/grpcRoutes/`", "type": "string"}, "rules": {"description": "Required. A list of detailed rules defining how to route traffic. Within a single GrpcRoute, the GrpcRoute.RouteAction associated with the first matching GrpcRoute.RouteRule will be executed. At least one rule must be supplied.", "items": {"$ref": "GrpcRouteRouteRule"}, "type": "array"}, "selfLink": {"description": "Output only. Server-defined URL of this resource", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GrpcRouteDestination": {"description": "The destination to which traffic will be routed.", "id": "GrpcRouteDestination", "properties": {"serviceName": {"description": "Required. The URL of a destination service to which to route traffic. Must refer to either a BackendService or ServiceDirectoryService.", "type": "string"}, "weight": {"description": "Optional. Specifies the proportion of requests forwarded to the backend referenced by the serviceName field. This is computed as: - weight/Sum(weights in this destination list). For non-zero values, there may be some epsilon from the exact proportion defined here depending on the precision an implementation supports. If only one serviceName is specified and it has a weight greater than 0, 100% of the traffic is forwarded to that backend. If weights are specified for any one service name, they need to be specified for all of them. If weights are unspecified for all services, then, traffic is distributed in equal proportions to all of them.", "format": "int32", "type": "integer"}}, "type": "object"}, "GrpcRouteFaultInjectionPolicy": {"description": "The specification for fault injection introduced into traffic to test the resiliency of clients to destination service failure. As part of fault injection, when clients send requests to a destination, delays can be introduced on a percentage of requests before sending those requests to the destination service. Similarly requests from clients can be aborted by for a percentage of requests.", "id": "GrpcRouteFaultInjectionPolicy", "properties": {"abort": {"$ref": "GrpcRouteFaultInjectionPolicyAbort", "description": "The specification for aborting to client requests."}, "delay": {"$ref": "GrpcRouteFaultInjectionPolicyDelay", "description": "The specification for injecting delay to client requests."}}, "type": "object"}, "GrpcRouteFaultInjectionPolicyAbort": {"description": "Specification of how client requests are aborted as part of fault injection before being sent to a destination.", "id": "GrpcRouteFaultInjectionPolicyAbort", "properties": {"httpStatus": {"description": "The HTTP status code used to abort the request. The value must be between 200 and 599 inclusive.", "format": "int32", "type": "integer"}, "percentage": {"description": "The percentage of traffic which will be aborted. The value must be between [0, 100]", "format": "int32", "type": "integer"}}, "type": "object"}, "GrpcRouteFaultInjectionPolicyDelay": {"description": "Specification of how client requests are delayed as part of fault injection before being sent to a destination.", "id": "GrpcRouteFaultInjectionPolicyDelay", "properties": {"fixedDelay": {"description": "Specify a fixed delay before forwarding the request.", "format": "google-duration", "type": "string"}, "percentage": {"description": "The percentage of traffic on which delay will be injected. The value must be between [0, 100]", "format": "int32", "type": "integer"}}, "type": "object"}, "GrpcRouteHeaderMatch": {"description": "A match against a collection of headers.", "id": "GrpcRouteHeaderMatch", "properties": {"key": {"description": "Required. The key of the header.", "type": "string"}, "type": {"description": "Optional. Specifies how to match against the value of the header. If not specified, a default value of EXACT is used.", "enum": ["TYPE_UNSPECIFIED", "EXACT", "REGULAR_EXPRESSION"], "enumDescriptions": ["Unspecified.", "Will only match the exact value provided.", "Will match paths conforming to the prefix specified by value. RE2 syntax is supported."], "type": "string"}, "value": {"description": "Required. The value of the header.", "type": "string"}}, "type": "object"}, "GrpcRouteMethodMatch": {"description": "Specifies a match against a method.", "id": "GrpcRouteMethodMatch", "properties": {"caseSensitive": {"description": "Optional. Specifies that matches are case sensitive. The default value is true. case_sensitive must not be used with a type of REGULAR_EXPRESSION.", "type": "boolean"}, "grpcMethod": {"description": "Required. Name of the method to match against. If unspecified, will match all methods.", "type": "string"}, "grpcService": {"description": "Required. Name of the service to match against. If unspecified, will match all services.", "type": "string"}, "type": {"description": "Optional. Specifies how to match against the name. If not specified, a default value of \"EXACT\" is used.", "enum": ["TYPE_UNSPECIFIED", "EXACT", "REGULAR_EXPRESSION"], "enumDescriptions": ["Unspecified.", "Will only match the exact name provided.", "Will interpret grpc_method and grpc_service as regexes. RE2 syntax is supported."], "type": "string"}}, "type": "object"}, "GrpcRouteRetryPolicy": {"description": "The specifications for retries. Specifies one or more conditions for which this retry rule applies. Valid values are:", "id": "GrpcRouteRetryPolicy", "properties": {"numRetries": {"description": "Specifies the allowed number of retries. This number must be > 0. If not specified, default to 1.", "format": "uint32", "type": "integer"}, "retryConditions": {"description": "- connect-failure: Router will retry on failures connecting to Backend Services, for example due to connection timeouts. - refused-stream: Router will retry if the backend service resets the stream with a REFUSED_STREAM error code. This reset type indicates that it is safe to retry. - cancelled: Router will retry if the gRPC status code in the response header is set to cancelled - deadline-exceeded: Router will retry if the gRPC status code in the response header is set to deadline-exceeded - resource-exhausted: Router will retry if the gRPC status code in the response header is set to resource-exhausted - unavailable: Router will retry if the gRPC status code in the response header is set to unavailable", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GrpcRouteRouteAction": {"description": "Specifies how to route matched traffic.", "id": "GrpcRouteRouteAction", "properties": {"destinations": {"description": "Optional. The destination services to which traffic should be forwarded. If multiple destinations are specified, traffic will be split between Backend Service(s) according to the weight field of these destinations.", "items": {"$ref": "GrpcRouteDestination"}, "type": "array"}, "faultInjectionPolicy": {"$ref": "GrpcRouteFaultInjectionPolicy", "description": "Optional. The specification for fault injection introduced into traffic to test the resiliency of clients to destination service failure. As part of fault injection, when clients send requests to a destination, delays can be introduced on a percentage of requests before sending those requests to the destination service. Similarly requests from clients can be aborted by for a percentage of requests. timeout and retry_policy will be ignored by clients that are configured with a fault_injection_policy"}, "idleTimeout": {"description": "Optional. Specifies the idle timeout for the selected route. The idle timeout is defined as the period in which there are no bytes sent or received on either the upstream or downstream connection. If not set, the default idle timeout is 1 hour. If set to 0s, the timeout will be disabled.", "format": "google-duration", "type": "string"}, "retryPolicy": {"$ref": "GrpcRouteRetryPolicy", "description": "Optional. Specifies the retry policy associated with this route."}, "statefulSessionAffinity": {"$ref": "GrpcRouteStatefulSessionAffinityPolicy", "description": "Optional. Specifies cookie-based stateful session affinity."}, "timeout": {"description": "Optional. Specifies the timeout for selected route. Timeout is computed from the time the request has been fully processed (i.e. end of stream) up until the response has been completely processed. Timeout includes all retries.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GrpcRouteRouteMatch": {"description": "Criteria for matching traffic. A RouteMatch will be considered to match when all supplied fields match.", "id": "GrpcRouteRouteMatch", "properties": {"headers": {"description": "Optional. Specifies a collection of headers to match.", "items": {"$ref": "GrpcRouteHeaderMatch"}, "type": "array"}, "method": {"$ref": "GrpcRouteMethodMatch", "description": "Optional. A gRPC method to match against. If this field is empty or omitted, will match all methods."}}, "type": "object"}, "GrpcRouteRouteRule": {"description": "Describes how to route traffic.", "id": "GrpcRouteRouteRule", "properties": {"action": {"$ref": "GrpcRouteRouteAction", "description": "Required. A detailed rule defining how to route traffic. This field is required."}, "matches": {"description": "Optional. Matches define conditions used for matching the rule against incoming gRPC requests. Each match is independent, i.e. this rule will be matched if ANY one of the matches is satisfied. If no matches field is specified, this rule will unconditionally match traffic.", "items": {"$ref": "GrpcRouteRouteMatch"}, "type": "array"}}, "type": "object"}, "GrpcRouteStatefulSessionAffinityPolicy": {"description": "The specification for cookie-based stateful session affinity where the date plane supplies a “session cookie” with the name \"GSSA\" which encodes a specific destination host and each request containing that cookie will be directed to that host as long as the destination host remains up and healthy. The gRPC proxyless mesh library or sidecar proxy will manage the session cookie but the client application code is responsible for copying the cookie from each RPC in the session to the next.", "id": "GrpcRouteStatefulSessionAffinityPolicy", "properties": {"cookieTtl": {"description": "Required. The cookie TTL value for the Set-Cookie header generated by the data plane. The lifetime of the cookie may be set to a value from 0 to 86400 seconds (24 hours) inclusive. Set this to 0s to use a session cookie and disable cookie expiration.", "format": "google-duration", "type": "string"}}, "type": "object"}, "HttpRoute": {"description": "HttpRoute is the resource defining how HTTP traffic should be routed by a Mesh or Gateway resource.", "id": "HttpRoute", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "gateways": {"description": "Optional. Gateways defines a list of gateways this HttpRoute is attached to, as one of the routing rules to route the requests served by the gateway. Each gateway reference should match the pattern: `projects/*/locations/global/gateways/`", "items": {"type": "string"}, "type": "array"}, "hostnames": {"description": "Required. Hostnames define a set of hosts that should match against the HTTP host header to select a HttpRoute to process the request. Hostname is the fully qualified domain name of a network host, as defined by RFC 1123 with the exception that: - IPs are not allowed. - A hostname may be prefixed with a wildcard label (`*.`). The wildcard label must appear by itself as the first label. Hostname can be \"precise\" which is a domain name without the terminating dot of a network host (e.g. `foo.example.com`) or \"wildcard\", which is a domain name prefixed with a single wildcard label (e.g. `*.example.com`). Note that as per RFC1035 and RFC1123, a label must consist of lower case alphanumeric characters or '-', and must start and end with an alphanumeric character. No other punctuation is allowed. The routes associated with a Mesh or Gateways must have unique hostnames. If you attempt to attach multiple routes with conflicting hostnames, the configuration will be rejected. For example, while it is acceptable for routes for the hostnames `*.foo.bar.com` and `*.bar.com` to be associated with the same Mesh (or Gateways under the same scope), it is not possible to associate two routes both with `*.bar.com` or both with `bar.com`.", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the HttpRoute resource.", "type": "object"}, "meshes": {"description": "Optional. Meshes defines a list of meshes this HttpRoute is attached to, as one of the routing rules to route the requests served by the mesh. Each mesh reference should match the pattern: `projects/*/locations/global/meshes/` The attached Mesh should be of a type SIDECAR", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Identifier. Name of the HttpRoute resource. It matches pattern `projects/*/locations/global/httpRoutes/http_route_name>`.", "type": "string"}, "rules": {"description": "Required. Rules that define how traffic is routed and handled. Rules will be matched sequentially based on the RouteMatch specified for the rule.", "items": {"$ref": "HttpRouteRouteRule"}, "type": "array"}, "selfLink": {"description": "Output only. Server-defined URL of this resource", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "HttpRouteCorsPolicy": {"description": "The Specification for allowing client side cross-origin requests.", "id": "HttpRouteCorsPolicy", "properties": {"allowCredentials": {"description": "In response to a preflight request, setting this to true indicates that the actual request can include user credentials. This translates to the Access-Control-Allow-Credentials header. Default value is false.", "type": "boolean"}, "allowHeaders": {"description": "Specifies the content for Access-Control-Allow-Headers header.", "items": {"type": "string"}, "type": "array"}, "allowMethods": {"description": "Specifies the content for Access-Control-Allow-Methods header.", "items": {"type": "string"}, "type": "array"}, "allowOriginRegexes": {"description": "Specifies the regular expression patterns that match allowed origins. For regular expression grammar, please see https://github.com/google/re2/wiki/Syntax.", "items": {"type": "string"}, "type": "array"}, "allowOrigins": {"description": "Specifies the list of origins that will be allowed to do CORS requests. An origin is allowed if it matches either an item in allow_origins or an item in allow_origin_regexes.", "items": {"type": "string"}, "type": "array"}, "disabled": {"description": "If true, the CORS policy is disabled. The default value is false, which indicates that the CORS policy is in effect.", "type": "boolean"}, "exposeHeaders": {"description": "Specifies the content for Access-Control-Expose-Headers header.", "items": {"type": "string"}, "type": "array"}, "maxAge": {"description": "Specifies how long result of a preflight request can be cached in seconds. This translates to the Access-Control-Max-Age header.", "type": "string"}}, "type": "object"}, "HttpRouteDestination": {"description": "Specifications of a destination to which the request should be routed to.", "id": "HttpRouteDestination", "properties": {"requestHeaderModifier": {"$ref": "HttpRouteHeaderModifier", "description": "Optional. The specification for modifying the headers of a matching request prior to delivery of the request to the destination. If HeaderModifiers are set on both the Destination and the RouteAction, they will be merged. Conflicts between the two will not be resolved on the configuration."}, "responseHeaderModifier": {"$ref": "HttpRouteHeaderModifier", "description": "Optional. The specification for modifying the headers of a response prior to sending the response back to the client. If HeaderModifiers are set on both the Destination and the RouteAction, they will be merged. Conflicts between the two will not be resolved on the configuration."}, "serviceName": {"description": "The URL of a BackendService to route traffic to.", "type": "string"}, "weight": {"description": "Specifies the proportion of requests forwarded to the backend referenced by the serviceName field. This is computed as: - weight/Sum(weights in this destination list). For non-zero values, there may be some epsilon from the exact proportion defined here depending on the precision an implementation supports. If only one serviceName is specified and it has a weight greater than 0, 100% of the traffic is forwarded to that backend. If weights are specified for any one service name, they need to be specified for all of them. If weights are unspecified for all services, then, traffic is distributed in equal proportions to all of them.", "format": "int32", "type": "integer"}}, "type": "object"}, "HttpRouteFaultInjectionPolicy": {"description": "The specification for fault injection introduced into traffic to test the resiliency of clients to destination service failure. As part of fault injection, when clients send requests to a destination, delays can be introduced by client proxy on a percentage of requests before sending those requests to the destination service. Similarly requests can be aborted by client proxy for a percentage of requests.", "id": "HttpRouteFaultInjectionPolicy", "properties": {"abort": {"$ref": "HttpRouteFaultInjectionPolicyAbort", "description": "The specification for aborting to client requests."}, "delay": {"$ref": "HttpRouteFaultInjectionPolicyDelay", "description": "The specification for injecting delay to client requests."}}, "type": "object"}, "HttpRouteFaultInjectionPolicyAbort": {"description": "Specification of how client requests are aborted as part of fault injection before being sent to a destination.", "id": "HttpRouteFaultInjectionPolicyAbort", "properties": {"httpStatus": {"description": "The HTTP status code used to abort the request. The value must be between 200 and 599 inclusive.", "format": "int32", "type": "integer"}, "percentage": {"description": "The percentage of traffic which will be aborted. The value must be between [0, 100]", "format": "int32", "type": "integer"}}, "type": "object"}, "HttpRouteFaultInjectionPolicyDelay": {"description": "Specification of how client requests are delayed as part of fault injection before being sent to a destination.", "id": "HttpRouteFaultInjectionPolicyDelay", "properties": {"fixedDelay": {"description": "Specify a fixed delay before forwarding the request.", "format": "google-duration", "type": "string"}, "percentage": {"description": "The percentage of traffic on which delay will be injected. The value must be between [0, 100]", "format": "int32", "type": "integer"}}, "type": "object"}, "HttpRouteHeaderMatch": {"description": "Specifies how to select a route rule based on HTTP request headers.", "id": "HttpRouteHeaderMatch", "properties": {"exactMatch": {"description": "The value of the header should match exactly the content of exact_match.", "type": "string"}, "header": {"description": "The name of the HTTP header to match against.", "type": "string"}, "invertMatch": {"description": "If specified, the match result will be inverted before checking. Default value is set to false.", "type": "boolean"}, "prefixMatch": {"description": "The value of the header must start with the contents of prefix_match.", "type": "string"}, "presentMatch": {"description": "A header with header_name must exist. The match takes place whether or not the header has a value.", "type": "boolean"}, "rangeMatch": {"$ref": "HttpRouteHeaderMatchIntegerRange", "description": "If specified, the rule will match if the request header value is within the range."}, "regexMatch": {"description": "The value of the header must match the regular expression specified in regex_match. For regular expression grammar, please see: https://github.com/google/re2/wiki/Syntax", "type": "string"}, "suffixMatch": {"description": "The value of the header must end with the contents of suffix_match.", "type": "string"}}, "type": "object"}, "HttpRouteHeaderMatchIntegerRange": {"description": "Represents an integer value range.", "id": "HttpRouteHeaderMatchIntegerRange", "properties": {"end": {"description": "End of the range (exclusive)", "format": "int32", "type": "integer"}, "start": {"description": "Start of the range (inclusive)", "format": "int32", "type": "integer"}}, "type": "object"}, "HttpRouteHeaderModifier": {"description": "The specification for modifying HTTP header in HTTP request and HTTP response.", "id": "HttpRouteHeaderModifier", "properties": {"add": {"additionalProperties": {"type": "string"}, "description": "Add the headers with given map where key is the name of the header, value is the value of the header.", "type": "object"}, "remove": {"description": "Remove headers (matching by header names) specified in the list.", "items": {"type": "string"}, "type": "array"}, "set": {"additionalProperties": {"type": "string"}, "description": "Completely overwrite/replace the headers with given map where key is the name of the header, value is the value of the header.", "type": "object"}}, "type": "object"}, "HttpRouteHttpDirectResponse": {"description": "Static HTTP response object to be returned.", "id": "HttpRouteHttpDirectResponse", "properties": {"bytesBody": {"description": "Optional. Response body as bytes. Maximum body size is 4096B.", "format": "byte", "type": "string"}, "status": {"description": "Required. Status to return as part of HTTP Response. Must be a positive integer.", "format": "int32", "type": "integer"}, "stringBody": {"description": "Optional. Response body as a string. Maximum body length is 1024 characters.", "type": "string"}}, "type": "object"}, "HttpRouteQueryParameterMatch": {"description": "Specifications to match a query parameter in the request.", "id": "HttpRouteQueryParameterMatch", "properties": {"exactMatch": {"description": "The value of the query parameter must exactly match the contents of exact_match. Only one of exact_match, regex_match, or present_match must be set.", "type": "string"}, "presentMatch": {"description": "Specifies that the QueryParameterMatcher matches if request contains query parameter, irrespective of whether the parameter has a value or not. Only one of exact_match, regex_match, or present_match must be set.", "type": "boolean"}, "queryParameter": {"description": "The name of the query parameter to match.", "type": "string"}, "regexMatch": {"description": "The value of the query parameter must match the regular expression specified by regex_match. For regular expression grammar, please see https://github.com/google/re2/wiki/Syntax Only one of exact_match, regex_match, or present_match must be set.", "type": "string"}}, "type": "object"}, "HttpRouteRedirect": {"description": "The specification for redirecting traffic.", "id": "HttpRouteRedirect", "properties": {"hostRedirect": {"description": "The host that will be used in the redirect response instead of the one that was supplied in the request.", "type": "string"}, "httpsRedirect": {"description": "If set to true, the URL scheme in the redirected request is set to https. If set to false, the URL scheme of the redirected request will remain the same as that of the request. The default is set to false.", "type": "boolean"}, "pathRedirect": {"description": "The path that will be used in the redirect response instead of the one that was supplied in the request. path_redirect can not be supplied together with prefix_redirect. Supply one alone or neither. If neither is supplied, the path of the original request will be used for the redirect.", "type": "string"}, "portRedirect": {"description": "The port that will be used in the redirected request instead of the one that was supplied in the request.", "format": "int32", "type": "integer"}, "prefixRewrite": {"description": "Indicates that during redirection, the matched prefix (or path) should be swapped with this value. This option allows URLs be dynamically created based on the request.", "type": "string"}, "responseCode": {"description": "The HTTP Status code to use for the redirect.", "enum": ["RESPONSE_CODE_UNSPECIFIED", "MOVED_PERMANENTLY_DEFAULT", "FOUND", "SEE_OTHER", "TEMPORARY_REDIRECT", "PERMANENT_REDIRECT"], "enumDescriptions": ["Default value", "Corresponds to 301.", "Corresponds to 302.", "Corresponds to 303.", "Corresponds to 307. In this case, the request method will be retained.", "Corresponds to 308. In this case, the request method will be retained."], "type": "string"}, "stripQuery": {"description": "if set to true, any accompanying query portion of the original URL is removed prior to redirecting the request. If set to false, the query portion of the original URL is retained. The default is set to false.", "type": "boolean"}}, "type": "object"}, "HttpRouteRequestMirrorPolicy": {"description": "Specifies the policy on how requests are shadowed to a separate mirrored destination service. The proxy does not wait for responses from the shadow service. Prior to sending traffic to the shadow service, the host/authority header is suffixed with -shadow. Mirroring is currently not supported for Cloud Run destinations.", "id": "HttpRouteRequestMirrorPolicy", "properties": {"destination": {"$ref": "HttpRouteDestination", "description": "The destination the requests will be mirrored to. The weight of the destination will be ignored."}, "mirrorPercent": {"description": "Optional. The percentage of requests to get mirrored to the desired destination.", "format": "float", "type": "number"}}, "type": "object"}, "HttpRouteRetryPolicy": {"description": "The specifications for retries.", "id": "HttpRouteRetryPolicy", "properties": {"numRetries": {"description": "Specifies the allowed number of retries. This number must be > 0. If not specified, default to 1.", "format": "int32", "type": "integer"}, "perTryTimeout": {"description": "Specifies a non-zero timeout per retry attempt.", "format": "google-duration", "type": "string"}, "retryConditions": {"description": "Specifies one or more conditions when this retry policy applies. Valid values are: 5xx: Proxy will attempt a retry if the destination service responds with any 5xx response code, of if the destination service does not respond at all, example: disconnect, reset, read timeout, connection failure and refused streams. gateway-error: Similar to 5xx, but only applies to response codes 502, 503, 504. reset: Proxy will attempt a retry if the destination service does not respond at all (disconnect/reset/read timeout) connect-failure: Proxy will retry on failures connecting to destination for example due to connection timeouts. retriable-4xx: Proxy will retry fro retriable 4xx response codes. Currently the only retriable error supported is 409. refused-stream: Proxy will retry if the destination resets the stream with a REFUSED_STREAM error code. This reset type indicates that it is safe to retry.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "HttpRouteRouteAction": {"description": "The specifications for routing traffic and applying associated policies.", "id": "HttpRouteRouteAction", "properties": {"corsPolicy": {"$ref": "HttpRouteCorsPolicy", "description": "The specification for allowing client side cross-origin requests."}, "destinations": {"description": "The destination to which traffic should be forwarded.", "items": {"$ref": "HttpRouteDestination"}, "type": "array"}, "directResponse": {"$ref": "HttpRouteHttpDirectResponse", "description": "Optional. Static HTTP Response object to be returned regardless of the request."}, "faultInjectionPolicy": {"$ref": "HttpRouteFaultInjectionPolicy", "description": "The specification for fault injection introduced into traffic to test the resiliency of clients to backend service failure. As part of fault injection, when clients send requests to a backend service, delays can be introduced on a percentage of requests before sending those requests to the backend service. Similarly requests from clients can be aborted for a percentage of requests. timeout and retry_policy will be ignored by clients that are configured with a fault_injection_policy"}, "idleTimeout": {"description": "Optional. Specifies the idle timeout for the selected route. The idle timeout is defined as the period in which there are no bytes sent or received on either the upstream or downstream connection. If not set, the default idle timeout is 1 hour. If set to 0s, the timeout will be disabled.", "format": "google-duration", "type": "string"}, "redirect": {"$ref": "HttpRouteRedirect", "description": "If set, the request is directed as configured by this field."}, "requestHeaderModifier": {"$ref": "HttpRouteHeaderModifier", "description": "The specification for modifying the headers of a matching request prior to delivery of the request to the destination. If HeaderModifiers are set on both the Destination and the RouteAction, they will be merged. Conflicts between the two will not be resolved on the configuration."}, "requestMirrorPolicy": {"$ref": "HttpRouteRequestMirrorPolicy", "description": "Specifies the policy on how requests intended for the routes destination are shadowed to a separate mirrored destination. Proxy will not wait for the shadow destination to respond before returning the response. Prior to sending traffic to the shadow service, the host/authority header is suffixed with -shadow."}, "responseHeaderModifier": {"$ref": "HttpRouteHeaderModifier", "description": "The specification for modifying the headers of a response prior to sending the response back to the client. If HeaderModifiers are set on both the Destination and the RouteAction, they will be merged. Conflicts between the two will not be resolved on the configuration."}, "retryPolicy": {"$ref": "HttpRouteRetryPolicy", "description": "Specifies the retry policy associated with this route."}, "statefulSessionAffinity": {"$ref": "HttpRouteStatefulSessionAffinityPolicy", "description": "Optional. Specifies cookie-based stateful session affinity."}, "timeout": {"description": "Specifies the timeout for selected route. Timeout is computed from the time the request has been fully processed (i.e. end of stream) up until the response has been completely processed. Timeout includes all retries.", "format": "google-duration", "type": "string"}, "urlRewrite": {"$ref": "HttpRouteURLRewrite", "description": "The specification for rewrite URL before forwarding requests to the destination."}}, "type": "object"}, "HttpRouteRouteMatch": {"description": "RouteMatch defines specifications used to match requests. If multiple match types are set, this RouteMatch will match if ALL type of matches are matched.", "id": "HttpRouteRouteMatch", "properties": {"fullPathMatch": {"description": "The HTTP request path value should exactly match this value. Only one of full_path_match, prefix_match, or regex_match should be used.", "type": "string"}, "headers": {"description": "Specifies a list of HTTP request headers to match against. ALL of the supplied headers must be matched.", "items": {"$ref": "HttpRouteHeaderMatch"}, "type": "array"}, "ignoreCase": {"description": "Specifies if prefix_match and full_path_match matches are case sensitive. The default value is false.", "type": "boolean"}, "prefixMatch": {"description": "The HTTP request path value must begin with specified prefix_match. prefix_match must begin with a /. Only one of full_path_match, prefix_match, or regex_match should be used.", "type": "string"}, "queryParameters": {"description": "Specifies a list of query parameters to match against. ALL of the query parameters must be matched.", "items": {"$ref": "HttpRouteQueryParameterMatch"}, "type": "array"}, "regexMatch": {"description": "The HTTP request path value must satisfy the regular expression specified by regex_match after removing any query parameters and anchor supplied with the original URL. For regular expression grammar, please see https://github.com/google/re2/wiki/Syntax Only one of full_path_match, prefix_match, or regex_match should be used.", "type": "string"}}, "type": "object"}, "HttpRouteRouteRule": {"description": "Specifies how to match traffic and how to route traffic when traffic is matched.", "id": "HttpRouteRouteRule", "properties": {"action": {"$ref": "HttpRouteRouteAction", "description": "The detailed rule defining how to route matched traffic."}, "matches": {"description": "A list of matches define conditions used for matching the rule against incoming HTTP requests. Each match is independent, i.e. this rule will be matched if ANY one of the matches is satisfied. If no matches field is specified, this rule will unconditionally match traffic. If a default rule is desired to be configured, add a rule with no matches specified to the end of the rules list.", "items": {"$ref": "HttpRouteRouteMatch"}, "type": "array"}}, "type": "object"}, "HttpRouteStatefulSessionAffinityPolicy": {"description": "The specification for cookie-based stateful session affinity where the date plane supplies a “session cookie” with the name \"GSSA\" which encodes a specific destination host and each request containing that cookie will be directed to that host as long as the destination host remains up and healthy. The gRPC proxyless mesh library or sidecar proxy will manage the session cookie but the client application code is responsible for copying the cookie from each RPC in the session to the next.", "id": "HttpRouteStatefulSessionAffinityPolicy", "properties": {"cookieTtl": {"description": "Required. The cookie TTL value for the Set-Cookie header generated by the data plane. The lifetime of the cookie may be set to a value from 0 to 86400 seconds (24 hours) inclusive. Set this to 0s to use a session cookie and disable cookie expiration.", "format": "google-duration", "type": "string"}}, "type": "object"}, "HttpRouteURLRewrite": {"description": "The specification for modifying the URL of the request, prior to forwarding the request to the destination.", "id": "HttpRouteURLRewrite", "properties": {"hostRewrite": {"description": "Prior to forwarding the request to the selected destination, the requests host header is replaced by this value.", "type": "string"}, "pathPrefixRewrite": {"description": "Prior to forwarding the request to the selected destination, the matching portion of the requests path is replaced by this value.", "type": "string"}}, "type": "object"}, "LbEdgeExtension": {"description": "`LbEdgeExtension` is a resource that lets the extension service influence the selection of backend services and Cloud CDN cache keys by modifying request headers.", "id": "LbEdgeExtension", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "extensionChains": {"description": "Required. A set of ordered extension chains that contain the match conditions and extensions to execute. Match conditions for each extension chain are evaluated in sequence for a given request. The first extension chain that has a condition that matches the request is executed. Any subsequent extension chains do not execute. Limited to 5 extension chains per resource.", "items": {"$ref": "ExtensionChain"}, "type": "array"}, "forwardingRules": {"description": "Required. A list of references to the forwarding rules to which this service extension is attached. At least one forwarding rule is required. Only one `LbEdgeExtension` resource can be associated with a forwarding rule.", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `LbEdgeExtension` resource. The format must comply with [the requirements for labels](https://cloud.google.com/compute/docs/labeling-resources#requirements) for Google Cloud resources.", "type": "object"}, "loadBalancingScheme": {"description": "Required. All forwarding rules referenced by this extension must share the same load balancing scheme. Supported values: `EXTERNAL_MANAGED`.", "enum": ["LOAD_BALANCING_SCHEME_UNSPECIFIED", "INTERNAL_MANAGED", "EXTERNAL_MANAGED"], "enumDescriptions": ["Default value. Do not use.", "Signifies that this is used for Internal HTTP(S) Load Balancing.", "Signifies that this is used for External Managed HTTP(S) Load Balancing."], "type": "string"}, "name": {"description": "Required. Identifier. Name of the `LbEdgeExtension` resource in the following format: `projects/{project}/locations/{location}/lbEdgeExtensions/{lb_edge_extension}`.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "LbRouteExtension": {"description": "`LbRouteExtension` is a resource that lets you control where traffic is routed to for a given request.", "id": "LbRouteExtension", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "extensionChains": {"description": "Required. A set of ordered extension chains that contain the match conditions and extensions to execute. Match conditions for each extension chain are evaluated in sequence for a given request. The first extension chain that has a condition that matches the request is executed. Any subsequent extension chains do not execute. Limited to 5 extension chains per resource.", "items": {"$ref": "ExtensionChain"}, "type": "array"}, "forwardingRules": {"description": "Required. A list of references to the forwarding rules to which this service extension is attached. At least one forwarding rule is required. Only one `LbRouteExtension` resource can be associated with a forwarding rule.", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `LbRouteExtension` resource. The format must comply with [the requirements for labels](https://cloud.google.com/compute/docs/labeling-resources#requirements) for Google Cloud resources.", "type": "object"}, "loadBalancingScheme": {"description": "Required. All backend services and forwarding rules referenced by this extension must share the same load balancing scheme. Supported values: `INTERNAL_MANAGED`, `EXTERNAL_MANAGED`. For more information, refer to [Backend services overview](https://cloud.google.com/load-balancing/docs/backend-service).", "enum": ["LOAD_BALANCING_SCHEME_UNSPECIFIED", "INTERNAL_MANAGED", "EXTERNAL_MANAGED"], "enumDescriptions": ["Default value. Do not use.", "Signifies that this is used for Internal HTTP(S) Load Balancing.", "Signifies that this is used for External Managed HTTP(S) Load Balancing."], "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. The metadata provided here is included as part of the `metadata_context` (of type `google.protobuf.Struct`) in the `ProcessingRequest` message sent to the extension server. The metadata applies to all extensions in all extensions chains in this resource. The metadata is available under the key `com.google.lb_route_extension.`. The following variables are supported in the metadata: `{forwarding_rule_id}` - substituted with the forwarding rule's fully qualified resource name. This field must not be set if at least one of the extension chains contains plugin extensions. Setting it results in a validation error. You can set metadata at either the resource level or the extension level. The extension level metadata is recommended because you can pass a different set of metadata through each extension to the backend.", "type": "object"}, "name": {"description": "Required. Identifier. Name of the `LbRouteExtension` resource in the following format: `projects/{project}/locations/{location}/lbRouteExtensions/{lb_route_extension}`.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "LbTrafficExtension": {"description": "`LbTrafficExtension` is a resource that lets the extension service modify the headers and payloads of both requests and responses without impacting the choice of backend services or any other security policies associated with the backend service.", "id": "LbTrafficExtension", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "extensionChains": {"description": "Required. A set of ordered extension chains that contain the match conditions and extensions to execute. Match conditions for each extension chain are evaluated in sequence for a given request. The first extension chain that has a condition that matches the request is executed. Any subsequent extension chains do not execute. Limited to 5 extension chains per resource.", "items": {"$ref": "ExtensionChain"}, "type": "array"}, "forwardingRules": {"description": "Optional. A list of references to the forwarding rules to which this service extension is attached. At least one forwarding rule is required. Only one `LbTrafficExtension` resource can be associated with a forwarding rule.", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `LbTrafficExtension` resource. The format must comply with [the requirements for labels](https://cloud.google.com/compute/docs/labeling-resources#requirements) for Google Cloud resources.", "type": "object"}, "loadBalancingScheme": {"description": "Required. All backend services and forwarding rules referenced by this extension must share the same load balancing scheme. Supported values: `INTERNAL_MANAGED` and `EXTERNAL_MANAGED`. For more information, refer to [Backend services overview](https://cloud.google.com/load-balancing/docs/backend-service).", "enum": ["LOAD_BALANCING_SCHEME_UNSPECIFIED", "INTERNAL_MANAGED", "EXTERNAL_MANAGED"], "enumDescriptions": ["Default value. Do not use.", "Signifies that this is used for Internal HTTP(S) Load Balancing.", "Signifies that this is used for External Managed HTTP(S) Load Balancing."], "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. The metadata provided here is included as part of the `metadata_context` (of type `google.protobuf.Struct`) in the `ProcessingRequest` message sent to the extension server. The metadata applies to all extensions in all extensions chains in this resource. The metadata is available under the key `com.google.lb_traffic_extension.`. The following variables are supported in the metadata: `{forwarding_rule_id}` - substituted with the forwarding rule's fully qualified resource name. This field must not be set if at least one of the extension chains contains plugin extensions. Setting it results in a validation error. You can set metadata at either the resource level or the extension level. The extension level metadata is recommended because you can pass a different set of metadata through each extension to the backend.", "type": "object"}, "name": {"description": "Required. Identifier. Name of the `LbTrafficExtension` resource in the following format: `projects/{project}/locations/{location}/lbTrafficExtensions/{lb_traffic_extension}`.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ListAuthzExtensionsResponse": {"description": "Message for response to listing `AuthzExtension` resources.", "id": "ListAuthzExtensionsResponse", "properties": {"authzExtensions": {"description": "The list of `AuthzExtension` resources.", "items": {"$ref": "AuthzExtension"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results that the server returns.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListEndpointPoliciesResponse": {"description": "Response returned by the ListEndpointPolicies method.", "id": "ListEndpointPoliciesResponse", "properties": {"endpointPolicies": {"description": "List of EndpointPolicy resources.", "items": {"$ref": "EndpointPolicy"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request opts into return_partial_success and reading across collections e.g. when attempting to list all resources across all supported locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGatewayRouteViewsResponse": {"description": "Response returned by the ListGatewayRouteViews method.", "id": "ListGatewayRouteViewsResponse", "properties": {"gatewayRouteViews": {"description": "List of GatewayRouteView resources.", "items": {"$ref": "GatewayRouteView"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request attempts to list all resources across all supported locations, while some locations are temporarily unavailable.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGatewaysResponse": {"description": "Response returned by the ListGateways method.", "id": "ListGatewaysResponse", "properties": {"gateways": {"description": "List of Gateway resources.", "items": {"$ref": "Gateway"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListGrpcRoutesResponse": {"description": "Response returned by the ListGrpcRoutes method.", "id": "ListGrpcRoutesResponse", "properties": {"grpcRoutes": {"description": "List of GrpcRoute resources.", "items": {"$ref": "GrpcRoute"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request opts into return_partial_success and reading across collections e.g. when attempting to list all resources across all supported locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListHttpRoutesResponse": {"description": "Response returned by the ListHttpRoutes method.", "id": "ListHttpRoutesResponse", "properties": {"httpRoutes": {"description": "List of HttpRoute resources.", "items": {"$ref": "HttpRoute"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request opts into return_partial_success and reading across collections e.g. when attempting to list all resources across all supported locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLbEdgeExtensionsResponse": {"description": "Message for response to listing `LbEdgeExtension` resources.", "id": "ListLbEdgeExtensionsResponse", "properties": {"lbEdgeExtensions": {"description": "The list of `LbEdgeExtension` resources.", "items": {"$ref": "LbEdgeExtension"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results that the server returns.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLbRouteExtensionsResponse": {"description": "Message for response to listing `LbRouteExtension` resources.", "id": "ListLbRouteExtensionsResponse", "properties": {"lbRouteExtensions": {"description": "The list of `LbRouteExtension` resources.", "items": {"$ref": "LbRouteExtension"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results that the server returns.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLbTrafficExtensionsResponse": {"description": "Message for response to listing `LbTrafficExtension` resources.", "id": "ListLbTrafficExtensionsResponse", "properties": {"lbTrafficExtensions": {"description": "The list of `LbTrafficExtension` resources.", "items": {"$ref": "LbTrafficExtension"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results that the server returns.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMeshRouteViewsResponse": {"description": "Response returned by the ListMeshRouteViews method.", "id": "ListMeshRouteViewsResponse", "properties": {"meshRouteViews": {"description": "List of MeshRouteView resources.", "items": {"$ref": "MeshRouteView"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request attempts to list all resources across all supported locations, while some locations are temporarily unavailable.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListMeshesResponse": {"description": "Response returned by the ListMeshes method.", "id": "ListMeshesResponse", "properties": {"meshes": {"description": "List of Mesh resources.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request opts into `return_partial_success` and reading across collections e.g. when attempting to list all resources across all supported locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListServiceBindingsResponse": {"description": "Response returned by the ListServiceBindings method.", "id": "ListServiceBindingsResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "serviceBindings": {"description": "List of ServiceBinding resources.", "items": {"$ref": "ServiceBinding"}, "type": "array"}, "unreachable": {"description": "Unreachable resources. Populated when the request attempts to list all resources across all supported locations, while some locations are temporarily unavailable.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListServiceLbPoliciesResponse": {"description": "Response returned by the ListServiceLbPolicies method.", "id": "ListServiceLbPoliciesResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "serviceLbPolicies": {"description": "List of ServiceLbPolicy resources.", "items": {"$ref": "ServiceLbPolicy"}, "type": "array"}, "unreachable": {"description": "Unreachable resources. Populated when the request attempts to list all resources across all supported locations, while some locations are temporarily unavailable.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTcpRoutesResponse": {"description": "Response returned by the ListTcpRoutes method.", "id": "ListTcpRoutesResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "tcpRoutes": {"description": "List of TcpRoute resources.", "items": {"$ref": "TcpRoute"}, "type": "array"}, "unreachable": {"description": "Unreachable resources. Populated when the request opts into return_partial_success and reading across collections e.g. when attempting to list all resources across all supported locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTlsRoutesResponse": {"description": "Response returned by the ListTlsRoutes method.", "id": "ListTlsRoutesResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "tlsRoutes": {"description": "List of TlsRoute resources.", "items": {"$ref": "TlsRoute"}, "type": "array"}, "unreachable": {"description": "Unreachable resources. Populated when the request opts into return_partial_success and reading across collections e.g. when attempting to list all resources across all supported locations.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListWasmPluginVersionsResponse": {"description": "Response returned by the `ListWasmPluginVersions` method.", "id": "ListWasmPluginVersionsResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request attempts to list all resources across all supported locations, while some locations are temporarily unavailable.", "items": {"type": "string"}, "type": "array"}, "wasmPluginVersions": {"description": "List of `WasmPluginVersion` resources.", "items": {"$ref": "WasmPluginVersion"}, "type": "array"}}, "type": "object"}, "ListWasmPluginsResponse": {"description": "Response returned by the `ListWasmPlugins` method.", "id": "ListWasmPluginsResponse", "properties": {"nextPageToken": {"description": "If there might be more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "unreachable": {"description": "Unreachable resources. Populated when the request attempts to list all resources across all supported locations, while some locations are temporarily unavailable.", "items": {"type": "string"}, "type": "array"}, "wasmPlugins": {"description": "List of `WasmPlugin` resources.", "items": {"$ref": "Wasm<PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LoggingConfig": {"description": "The configuration for Platform Telemetry logging for Eventarc Advanced resources.", "id": "LoggingConfig", "properties": {"logSeverity": {"description": "Optional. The minimum severity of logs that will be sent to Stackdriver/Platform Telemetry. Logs at severitiy ≥ this value will be sent, unless it is NONE.", "enum": ["LOG_SEVERITY_UNSPECIFIED", "NONE", "DEBUG", "INFO", "NOTICE", "WARNING", "ERROR", "CRITICAL", "ALERT", "EMERGENCY"], "enumDescriptions": ["Log severity is not specified. This value is treated the same as NONE, but is used to distinguish between no update and update to NONE in update_masks.", "Default value at resource creation, presence of this value must be treated as no logging/disable logging.", "Debug or trace level logging.", "Routine information, such as ongoing status or performance.", "Normal but significant events, such as start up, shut down, or a configuration change.", "Warning events might cause problems.", "Error events are likely to cause problems.", "Critical events cause more severe problems or outages.", "A person must take action immediately.", "One or more systems are unusable."], "type": "string"}}, "type": "object"}, "Mesh": {"description": "Mesh represents a logical configuration grouping for workload to workload communication within a service mesh. Routes that point to mesh dictate how requests are routed within this logical mesh boundary.", "id": "<PERSON><PERSON>", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "envoyHeaders": {"description": "Optional. Determines if envoy will insert internal debug headers into upstream requests. Other Envoy headers may still be injected. By default, envoy will not insert any debug headers.", "enum": ["ENVOY_HEADERS_UNSPECIFIED", "NONE", "DEBUG_HEADERS"], "enumDescriptions": ["Defaults to NONE.", "Suppress envoy debug headers.", "Envoy will insert default internal debug headers into upstream requests: x-envoy-attempt-count x-envoy-is-timeout-retry x-envoy-expected-rq-timeout-ms x-envoy-original-path x-envoy-upstream-stream-duration-ms"], "type": "string"}, "interceptionPort": {"description": "Optional. If set to a valid TCP port (1-65535), instructs the SIDECAR proxy to listen on the specified port of localhost (127.0.0.1) address. The SIDECAR proxy will expect all traffic to be redirected to this port regardless of its actual ip:port destination. If unset, a port '15001' is used as the interception port. This is applicable only for sidecar proxy deployments.", "format": "int32", "type": "integer"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the Mesh resource.", "type": "object"}, "name": {"description": "Identifier. Name of the Mesh resource. It matches pattern `projects/*/locations/global/meshes/`.", "type": "string"}, "selfLink": {"description": "Output only. Server-defined URL of this resource", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MeshRouteView": {"description": "MeshRouteView defines view-only resource for Routes to a Mesh", "id": "MeshRouteView", "properties": {"name": {"description": "Output only. Identifier. Full path name of the MeshRouteView resource. Format: projects/{project_number}/locations/{location}/meshes/{mesh}/routeViews/{route_view}", "readOnly": true, "type": "string"}, "routeId": {"description": "Output only. The resource id for the route.", "readOnly": true, "type": "string"}, "routeLocation": {"description": "Output only. Location where the route exists.", "readOnly": true, "type": "string"}, "routeProjectNumber": {"description": "Output only. Project number where the route exists.", "format": "int64", "readOnly": true, "type": "string"}, "routeType": {"description": "Output only. Type of the route: HttpRoute,GrpcRoute,TcpRoute, or TlsRoute", "readOnly": true, "type": "string"}}, "type": "object"}, "MetadataLabelMatcher": {"description": "The matcher that is based on node metadata presented by xDS clients.", "id": "MetadataLabelMatcher", "properties": {"metadataLabelMatchCriteria": {"description": "Specifies how matching should be done. Supported values are: MATCH_ANY: At least one of the Labels specified in the matcher should match the metadata presented by xDS client. MATCH_ALL: The metadata presented by the xDS client should contain all of the labels specified here. The selection is determined based on the best match. For example, suppose there are three EndpointPolicy resources P1, P2 and P3 and if P1 has a the matcher as MATCH_ANY , P2 has MATCH_ALL , and P3 has MATCH_ALL . If a client with label connects, the config from P1 will be selected. If a client with label connects, the config from P2 will be selected. If a client with label connects, the config from P3 will be selected. If there is more than one best match, (for example, if a config P4 with selector exists and if a client with label connects), pick up the one with older creation time.", "enum": ["METADATA_LABEL_MATCH_CRITERIA_UNSPECIFIED", "MATCH_ANY", "MATCH_ALL"], "enumDescriptions": ["Default value. Should not be used.", "At least one of the Labels specified in the matcher should match the metadata presented by xDS client.", "The metadata presented by the xDS client should contain all of the labels specified here."], "type": "string"}, "metadataLabels": {"description": "The list of label value pairs that must match labels in the provided metadata based on filterMatchCriteria This list can have at most 64 entries. The list can be empty if the match criteria is MATCH_ANY, to specify a wildcard match (i.e this matches any client).", "items": {"$ref": "MetadataLabels"}, "type": "array"}}, "type": "object"}, "MetadataLabels": {"description": "Defines a name-pair value for a single label.", "id": "MetadataLabels", "properties": {"labelName": {"description": "Required. Label name presented as key in xDS Node Metadata.", "type": "string"}, "labelValue": {"description": "Required. Label value presented as value corresponding to the above key, in xDS Node Metadata.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "RetryFilterPerRouteConfig": {"id": "RetryFilterPerRouteConfig", "properties": {"cryptoKeyName": {"description": "The name of the crypto key to use for encrypting event data.", "type": "string"}}, "type": "object"}, "ServiceBinding": {"description": "ServiceBinding can be used to: - Bind a Service Directory Service to be used in a BackendService resource. This feature will be deprecated soon. - Bind a Private Service Connect producer service to be used in consumer Cloud Service Mesh or Application Load Balancers. - Bind a Cloud Run service to be used in consumer Cloud Service Mesh or Application Load Balancers.", "id": "ServiceBinding", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the ServiceBinding resource.", "type": "object"}, "name": {"description": "Identifier. Name of the ServiceBinding resource. It matches pattern `projects/*/locations/*/serviceBindings/`.", "type": "string"}, "service": {"deprecated": true, "description": "Optional. The full Service Directory Service name of the format `projects/*/locations/*/namespaces/*/services/*`. This field is for Service Directory integration which will be deprecated soon.", "type": "string"}, "serviceId": {"deprecated": true, "description": "Output only. The unique identifier of the Service Directory Service against which the ServiceBinding resource is validated. This is populated when the Service Binding resource is used in another resource (like Backend Service). This is of the UUID4 format. This field is for Service Directory integration which will be deprecated soon.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceLbPolicy": {"description": "ServiceLbPolicy holds global load balancing and traffic distribution configuration that can be applied to a BackendService.", "id": "ServiceLbPolicy", "properties": {"autoCapacityDrain": {"$ref": "ServiceLbPolicyAutoCapacityDrain", "description": "Optional. Configuration to automatically move traffic away for unhealthy IG/NEG for the associated Backend Service."}, "createTime": {"description": "Output only. The timestamp when this resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "failoverConfig": {"$ref": "ServiceLbPolicyFailoverConfig", "description": "Optional. Configuration related to health based failover."}, "isolationConfig": {"$ref": "ServiceLbPolicyIsolationConfig", "description": "Optional. Configuration to provide isolation support for the associated Backend Service."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the ServiceLbPolicy resource.", "type": "object"}, "loadBalancingAlgorithm": {"description": "Optional. The type of load balancing algorithm to be used. The default behavior is WATERFALL_BY_REGION.", "enum": ["LOAD_BALANCING_ALGORITHM_UNSPECIFIED", "SPRAY_TO_WORLD", "SPRAY_TO_REGION", "WATERFALL_BY_REGION", "WATERFALL_BY_ZONE"], "enumDescriptions": ["The type of the loadbalancing algorithm is unspecified.", "Balance traffic across all backends across the world proportionally based on capacity.", "Direct traffic to the nearest region with endpoints and capacity before spilling over to other regions and spread the traffic from each client to all the MIGs/NEGs in a region.", "Direct traffic to the nearest region with endpoints and capacity before spilling over to other regions. All MIGs/NEGs within a region are evenly loaded but each client might not spread the traffic to all the MIGs/NEGs in the region.", "Attempt to keep traffic in a single zone closest to the client, before spilling over to other zones."], "type": "string"}, "name": {"description": "Identifier. Name of the ServiceLbPolicy resource. It matches pattern `projects/{project}/locations/{location}/serviceLbPolicies/{service_lb_policy_name}`.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when this resource was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ServiceLbPolicyAutoCapacityDrain": {"description": "Option to specify if an unhealthy IG/NEG should be considered for global load balancing and traffic routing.", "id": "ServiceLbPolicyAutoCapacityDrain", "properties": {"enable": {"description": "Optional. If set to 'True', an unhealthy IG/NEG will be set as drained. - An IG/NEG is considered unhealthy if less than 25% of the instances/endpoints in the IG/NEG are healthy. - This option will never result in draining more than 50% of the configured IGs/NEGs for the Backend Service.", "type": "boolean"}}, "type": "object"}, "ServiceLbPolicyFailoverConfig": {"description": "Option to specify health based failover behavior. This is not related to Network load balancer FailoverPolicy.", "id": "ServiceLbPolicyFailoverConfig", "properties": {"failoverHealthThreshold": {"description": "Optional. The percentage threshold that a load balancer will begin to send traffic to failover backends. If the percentage of endpoints in a MIG/NEG is smaller than this value, traffic would be sent to failover backends if possible. This field should be set to a value between 1 and 99. The default value is 50 for Global external HTTP(S) load balancer (classic) and Proxyless service mesh, and 70 for others.", "format": "int32", "type": "integer"}}, "type": "object"}, "ServiceLbPolicyIsolationConfig": {"description": "Configuration to provide isolation support for the associated Backend Service.", "id": "ServiceLbPolicyIsolationConfig", "properties": {"isolationGranularity": {"description": "Optional. The isolation granularity of the load balancer.", "enum": ["ISOLATION_GRANULARITY_UNSPECIFIED", "REGION"], "enumDescriptions": ["No isolation is configured for the backend service. Traffic can overflow based on the load balancing algorithm.", "Traffic for this service will be isolated at the cloud region level."], "type": "string"}, "isolationMode": {"description": "Optional. The isolation mode of the load balancer.", "enum": ["ISOLATION_MODE_UNSPECIFIED", "NEAREST", "STRICT"], "enumDescriptions": ["No isolation mode is configured for the backend service.", "Traffic will be sent to the nearest region.", "Traffic will fail if no serving backends are available in the same region as the load balancer."], "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TcpRoute": {"description": "TcpRoute is the resource defining how TCP traffic should be routed by a Mesh/Gateway resource.", "id": "TcpRoute", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "gateways": {"description": "Optional. Gateways defines a list of gateways this TcpRoute is attached to, as one of the routing rules to route the requests served by the gateway. Each gateway reference should match the pattern: `projects/*/locations/global/gateways/`", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the TcpRoute resource.", "type": "object"}, "meshes": {"description": "Optional. Meshes defines a list of meshes this TcpRoute is attached to, as one of the routing rules to route the requests served by the mesh. Each mesh reference should match the pattern: `projects/*/locations/global/meshes/` The attached Mesh should be of a type SIDECAR", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Identifier. Name of the TcpRoute resource. It matches pattern `projects/*/locations/global/tcpRoutes/tcp_route_name>`.", "type": "string"}, "rules": {"description": "Required. Rules that define how traffic is routed and handled. At least one RouteRule must be supplied. If there are multiple rules then the action taken will be the first rule to match.", "items": {"$ref": "TcpRouteRouteRule"}, "type": "array"}, "selfLink": {"description": "Output only. Server-defined URL of this resource", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "TcpRouteRouteAction": {"description": "The specifications for routing traffic and applying associated policies.", "id": "TcpRouteRouteAction", "properties": {"destinations": {"description": "Optional. The destination services to which traffic should be forwarded. At least one destination service is required. Only one of route destination or original destination can be set.", "items": {"$ref": "TcpRouteRouteDestination"}, "type": "array"}, "idleTimeout": {"description": "Optional. Specifies the idle timeout for the selected route. The idle timeout is defined as the period in which there are no bytes sent or received on either the upstream or downstream connection. If not set, the default idle timeout is 30 seconds. If set to 0s, the timeout will be disabled.", "format": "google-duration", "type": "string"}, "originalDestination": {"description": "Optional. If true, Router will use the destination IP and port of the original connection as the destination of the request. Default is false. Only one of route destinations or original destination can be set.", "type": "boolean"}}, "type": "object"}, "TcpRouteRouteDestination": {"description": "Describe the destination for traffic to be routed to.", "id": "TcpRouteRouteDestination", "properties": {"serviceName": {"description": "Required. The URL of a BackendService to route traffic to.", "type": "string"}, "weight": {"description": "Optional. Specifies the proportion of requests forwarded to the backend referenced by the serviceName field. This is computed as: - weight/Sum(weights in this destination list). For non-zero values, there may be some epsilon from the exact proportion defined here depending on the precision an implementation supports. If only one serviceName is specified and it has a weight greater than 0, 100% of the traffic is forwarded to that backend. If weights are specified for any one service name, they need to be specified for all of them. If weights are unspecified for all services, then, traffic is distributed in equal proportions to all of them.", "format": "int32", "type": "integer"}}, "type": "object"}, "TcpRouteRouteMatch": {"description": "RouteMatch defines the predicate used to match requests to a given action. Multiple match types are \"OR\"ed for evaluation. If no routeMatch field is specified, this rule will unconditionally match traffic.", "id": "TcpRouteRouteMatch", "properties": {"address": {"description": "Required. Must be specified in the CIDR range format. A CIDR range consists of an IP Address and a prefix length to construct the subnet mask. By default, the prefix length is 32 (i.e. matches a single IP address). Only IPV4 addresses are supported. Examples: \"********\" - matches against this exact IP address. \"10.0.0.0/8\" - matches against any IP address within the 10.0.0.0 subnet and ************* mask. \"0.0.0.0/0\" - matches against any IP address'.", "type": "string"}, "port": {"description": "Required. Specifies the destination port to match against.", "type": "string"}}, "type": "object"}, "TcpRouteRouteRule": {"description": "Specifies how to match traffic and how to route traffic when traffic is matched.", "id": "TcpRouteRouteRule", "properties": {"action": {"$ref": "TcpRouteRouteAction", "description": "Required. The detailed rule defining how to route matched traffic."}, "matches": {"description": "Optional. RouteMatch defines the predicate used to match requests to a given action. Multiple match types are \"OR\"ed for evaluation. If no routeMatch field is specified, this rule will unconditionally match traffic.", "items": {"$ref": "TcpRouteRouteMatch"}, "type": "array"}}, "type": "object"}, "TlsRoute": {"description": "TlsRoute defines how traffic should be routed based on SNI and other matching L3 attributes.", "id": "TlsRoute", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A free-text description of the resource. Max length 1024 characters.", "type": "string"}, "gateways": {"description": "Optional. Gateways defines a list of gateways this TlsRoute is attached to, as one of the routing rules to route the requests served by the gateway. Each gateway reference should match the pattern: `projects/*/locations/global/gateways/`", "items": {"type": "string"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of label tags associated with the TlsRoute resource.", "type": "object"}, "meshes": {"description": "Optional. Meshes defines a list of meshes this TlsRoute is attached to, as one of the routing rules to route the requests served by the mesh. Each mesh reference should match the pattern: `projects/*/locations/global/meshes/` The attached Mesh should be of a type SIDECAR", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Identifier. Name of the TlsRoute resource. It matches pattern `projects/*/locations/global/tlsRoutes/tls_route_name>`.", "type": "string"}, "rules": {"description": "Required. Rules that define how traffic is routed and handled. At least one RouteRule must be supplied. If there are multiple rules then the action taken will be the first rule to match.", "items": {"$ref": "TlsRouteRouteRule"}, "type": "array"}, "selfLink": {"description": "Output only. Server-defined URL of this resource", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "TlsRouteRouteAction": {"description": "The specifications for routing traffic and applying associated policies.", "id": "TlsRouteRouteAction", "properties": {"destinations": {"description": "Required. The destination services to which traffic should be forwarded. At least one destination service is required.", "items": {"$ref": "TlsRouteRouteDestination"}, "type": "array"}, "idleTimeout": {"description": "Optional. Specifies the idle timeout for the selected route. The idle timeout is defined as the period in which there are no bytes sent or received on either the upstream or downstream connection. If not set, the default idle timeout is 1 hour. If set to 0s, the timeout will be disabled.", "format": "google-duration", "type": "string"}}, "type": "object"}, "TlsRouteRouteDestination": {"description": "Describe the destination for traffic to be routed to.", "id": "TlsRouteRouteDestination", "properties": {"serviceName": {"description": "Required. The URL of a BackendService to route traffic to.", "type": "string"}, "weight": {"description": "Optional. Specifies the proportion of requests forwarded to the backend referenced by the service_name field. This is computed as: - weight/Sum(weights in destinations) Weights in all destinations does not need to sum up to 100.", "format": "int32", "type": "integer"}}, "type": "object"}, "TlsRouteRouteMatch": {"description": "RouteMatch defines the predicate used to match requests to a given action. Multiple match types are \"AND\"ed for evaluation.", "id": "TlsRouteRouteMatch", "properties": {"alpn": {"description": "Optional. ALPN (Application-Layer Protocol Negotiation) to match against. Examples: \"http/1.1\", \"h2\". At least one of sni_host and alpn is required. Up to 5 alpns across all matches can be set.", "items": {"type": "string"}, "type": "array"}, "sniHost": {"description": "Optional. SNI (server name indicator) to match against. SNI will be matched against all wildcard domains, i.e. `www.example.com` will be first matched against `www.example.com`, then `*.example.com`, then `*.com.` Partial wildcards are not supported, and values like *w.example.com are invalid. At least one of sni_host and alpn is required. Up to 100 sni hosts across all matches can be set.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TlsRouteRouteRule": {"description": "Specifies how to match traffic and how to route traffic when traffic is matched.", "id": "TlsRouteRouteRule", "properties": {"action": {"$ref": "TlsRouteRouteAction", "description": "Required. The detailed rule defining how to route matched traffic."}, "matches": {"description": "Required. RouteMatch defines the predicate used to match requests to a given action. Multiple match types are \"OR\"ed for evaluation. Atleast one RouteMatch must be supplied.", "items": {"$ref": "TlsRouteRouteMatch"}, "type": "array"}}, "type": "object"}, "TrafficPortSelector": {"description": "Specification of a port-based selector.", "id": "TrafficPortSelector", "properties": {"ports": {"description": "Optional. A list of ports. Can be port numbers or port range (example, [80-90] specifies all ports from 80 to 90, including 80 and 90) or named ports or * to specify all ports. If the list is empty, all ports are selected.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "WasmPlugin": {"description": "`WasmPlugin` is a resource representing a service executing a customer-provided Wasm module.", "id": "Wasm<PERSON><PERSON><PERSON>", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `WasmPlugin` resource. The format must comply with [the following requirements](/compute/docs/labeling-resources#requirements).", "type": "object"}, "logConfig": {"$ref": "WasmPluginLogConfig", "description": "Optional. Specifies the logging options for the activity performed by this plugin. If logging is enabled, plugin logs are exported to Cloud Logging. Note that the settings relate to the logs generated by using logging statements in your Wasm code."}, "mainVersionId": {"description": "Optional. The ID of the `WasmPluginVersion` resource that is the currently serving one. The version referred to must be a child of this `WasmPlugin` resource.", "type": "string"}, "name": {"description": "Identifier. Name of the `WasmPlugin` resource in the following format: `projects/{project}/locations/{location}/wasmPlugins/{wasm_plugin}`.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "usedBy": {"description": "Output only. List of all [extensions](https://cloud.google.com/service-extensions/docs/overview) that use this `WasmPlugin` resource.", "items": {"$ref": "WasmPluginUsedBy"}, "readOnly": true, "type": "array"}, "versions": {"additionalProperties": {"$ref": "WasmPluginVersionDetails"}, "description": "Optional. All versions of this `WasmPlugin` resource in the key-value format. The key is the resource ID, and the value is the `VersionDetails` object. Lets you create or update a `WasmPlugin` resource and its versions in a single request. When the `main_version_id` field is not empty, it must point to one of the `VersionDetails` objects in the map. If provided in a `PATCH` request, the new versions replace the previous set. Any version omitted from the `versions` field is removed. Because the `WasmPluginVersion` resource is immutable, if a `WasmPluginVersion` resource with the same name already exists and differs, the request fails. Note: In a `GET` request, this field is populated only if the field `GetWasmPluginRequest.view` is set to `WASM_PLUGIN_VIEW_FULL`.", "type": "object"}}, "type": "object"}, "WasmPluginLogConfig": {"description": "Specifies the logging options for the activity performed by this plugin. If logging is enabled, plugin logs are exported to Cloud Logging.", "id": "WasmPluginLogConfig", "properties": {"enable": {"description": "Optional. Specifies whether to enable logging for activity by this plugin. Defaults to `false`.", "type": "boolean"}, "minLogLevel": {"description": "Non-empty default. Specificies the lowest level of the plugin logs that are exported to Cloud Logging. This setting relates to the logs generated by using logging statements in your Wasm code. This field is can be set only if logging is enabled for the plugin. If the field is not provided when logging is enabled, it is set to `INFO` by default.", "enum": ["LOG_LEVEL_UNSPECIFIED", "TRACE", "DEBUG", "INFO", "WARN", "ERROR", "CRITICAL"], "enumDescriptions": ["Unspecified value. Defaults to `LogLevel.INFO`.", "Report logs with TRACE level and above.", "Report logs with DEBUG level and above.", "Report logs with INFO level and above.", "Report logs with WARN level and above.", "Report logs with ERROR level and above.", "Report logs with CRITICAL level only."], "type": "string"}, "sampleRate": {"description": "Non-empty default. Configures the sampling rate of activity logs, where `1.0` means all logged activity is reported and `0.0` means no activity is reported. A floating point value between `0.0` and `1.0` indicates that a percentage of log messages is stored. The default value when logging is enabled is `1.0`. The value of the field must be between `0` and `1` (inclusive). This field can be specified only if logging is enabled for this plugin.", "format": "float", "type": "number"}}, "type": "object"}, "WasmPluginUsedBy": {"description": "Defines a resource that uses the `WasmPlugin` resource.", "id": "WasmPluginUsedBy", "properties": {"name": {"description": "Output only. Full name of the resource https://google.aip.dev/122#full-resource-names, for example `//networkservices.googleapis.com/projects/{project}/locations/{location}/lbRouteExtensions/{extension}`", "readOnly": true, "type": "string"}}, "type": "object"}, "WasmPluginVersion": {"description": "A single immutable version of a `WasmPlugin` resource. Defines the Wasm module used and optionally its runtime config.", "id": "WasmPluginVersion", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "imageDigest": {"description": "Output only. The resolved digest for the image specified in the `image` field. The digest is resolved during the creation of `WasmPluginVersion` resource. This field holds the digest value, regardless of whether a tag or digest was originally specified in the `image` field.", "readOnly": true, "type": "string"}, "imageUri": {"description": "Optional. URI of the container image containing the plugin, stored in the Artifact Registry. When a new `WasmPluginVersion` resource is created, the digest of the container image is saved in the `image_digest` field. When downloading an image, the digest value is used instead of an image tag.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `WasmPluginVersion` resource.", "type": "object"}, "name": {"description": "Identifier. Name of the `WasmPluginVersion` resource in the following format: `projects/{project}/locations/{location}/wasmPlugins/{wasm_plugin}/ versions/{wasm_plugin_version}`.", "type": "string"}, "pluginConfigData": {"description": "Configuration for the plugin. The configuration is provided to the plugin at runtime through the `ON_CONFIGURE` callback. When a new `WasmPluginVersion` resource is created, the digest of the contents is saved in the `plugin_config_digest` field.", "format": "byte", "type": "string"}, "pluginConfigDigest": {"description": "Output only. This field holds the digest (usually checksum) value for the plugin configuration. The value is calculated based on the contents of `plugin_config_data` or the container image defined by the `plugin_config_uri` field.", "readOnly": true, "type": "string"}, "pluginConfigUri": {"description": "URI of the plugin configuration stored in the Artifact Registry. The configuration is provided to the plugin at runtime through the `ON_CONFIGURE` callback. The container image must contain only a single file with the name `plugin.config`. When a new `WasmPluginVersion` resource is created, the digest of the container image is saved in the `plugin_config_digest` field.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "WasmPluginVersionDetails": {"description": "Details of a `WasmPluginVersion` resource to be inlined in the `WasmPlugin` resource.", "id": "WasmPluginVersionDetails", "properties": {"createTime": {"description": "Output only. The timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A human-readable description of the resource.", "type": "string"}, "imageDigest": {"description": "Output only. The resolved digest for the image specified in `image`. The digest is resolved during the creation of a `WasmPluginVersion` resource. This field holds the digest value regardless of whether a tag or digest was originally specified in the `image` field.", "readOnly": true, "type": "string"}, "imageUri": {"description": "Optional. URI of the container image containing the Wasm module, stored in the Artifact Registry. The container image must contain only a single file with the name `plugin.wasm`. When a new `WasmPluginVersion` resource is created, the URI gets resolved to an image digest and saved in the `image_digest` field.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Set of labels associated with the `WasmPluginVersion` resource.", "type": "object"}, "pluginConfigData": {"description": "Configuration for the plugin. The configuration is provided to the plugin at runtime through the `ON_CONFIGURE` callback. When a new `WasmPluginVersion` version is created, the digest of the contents is saved in the `plugin_config_digest` field.", "format": "byte", "type": "string"}, "pluginConfigDigest": {"description": "Output only. This field holds the digest (usually checksum) value for the plugin configuration. The value is calculated based on the contents of the `plugin_config_data` field or the container image defined by the `plugin_config_uri` field.", "readOnly": true, "type": "string"}, "pluginConfigUri": {"description": "URI of the plugin configuration stored in the Artifact Registry. The configuration is provided to the plugin at runtime through the `ON_CONFIGURE` callback. The container image must contain only a single file with the name `plugin.config`. When a new `WasmPluginVersion` resource is created, the digest of the container image is saved in the `plugin_config_digest` field.", "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Network Services API", "version": "v1beta1", "version_module": true}