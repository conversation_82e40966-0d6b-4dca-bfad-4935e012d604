"""
Mock Discord objects for automated testing of the Discord Server Management Bot.
Simulates Discord API responses and guild/channel/role objects without requiring actual Discord connection.
"""
import asyncio
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, AsyncMock
from dataclasses import dataclass
from datetime import datetime, timezone

@dataclass
class MockPermissions:
    """Mock Discord permissions object."""
    administrator: bool = False
    manage_guild: bool = False
    manage_channels: bool = False
    manage_roles: bool = False
    kick_members: bool = False
    ban_members: bool = False
    manage_messages: bool = False
    view_channel: bool = True
    send_messages: bool = True
    add_reactions: bool = True
    mention_everyone: bool = False
    connect: bool = True
    speak: bool = True
    mute_members: bool = False
    deafen_members: bool = False
    move_members: bool = False
    use_slash_commands: bool = True
    embed_links: bool = True
    attach_files: bool = True
    read_message_history: bool = True
    manage_webhooks: bool = False

class MockRole:
    """Mock Discord role object."""
    def __init__(self, name: str, role_id: int = None, permissions: MockPermissions = None, managed: bool = False):
        self.id = role_id or hash(name) % 1000000
        self.name = name
        self.permissions = permissions or MockPermissions()
        self.managed = managed
        self.position = 1
        self.members = []
        self.color = 0
        self.hoist = False
        self.mentionable = True
        
    def __str__(self):
        return self.name

class MockChannel:
    """Mock Discord channel object."""
    def __init__(self, name: str, channel_type: str = "text", category=None, channel_id: int = None):
        self.id = channel_id or hash(name) % 1000000
        self.name = name
        self.type = channel_type
        self.category = category
        self.topic = None
        self.slowmode_delay = 0
        self.nsfw = False
        self.overwrites = {}
        self.position = 0
        
        # Mock methods
        self.edit = AsyncMock()
        self.delete = AsyncMock()
        self.send = AsyncMock()
        self.set_permissions = AsyncMock()
        
    def __str__(self):
        return f"#{self.name}"
    
    def overwrites_for(self, target):
        """Mock overwrites_for method."""
        return self.overwrites.get(target, MockPermissionOverwrite())

class MockCategory:
    """Mock Discord category channel."""
    def __init__(self, name: str, category_id: int = None):
        self.id = category_id or hash(name) % 1000000
        self.name = name
        self.channels = []
        self.overwrites = {}
        self.position = 0  # Add missing position attribute
        self.created_at = datetime.now(timezone.utc)
        self.category = None  # Categories don't have parent categories
        self.type = Mock()
        self.type.name = "category"

        # Mock methods
        self.edit = AsyncMock()
        self.delete = AsyncMock()
        self.create_text_channel = AsyncMock()
        self.create_voice_channel = AsyncMock()
        
    def __str__(self):
        return self.name

class MockPermissionOverwrite:
    """Mock Discord permission overwrite."""
    def __init__(self, **permissions):
        for perm, value in permissions.items():
            setattr(self, perm, value)

class MockGuild:
    """Mock Discord guild object with comprehensive server simulation."""
    def __init__(self, name: str = "Test Server", guild_id: int = 12345):
        self.id = guild_id
        self.name = name
        self.member_count = 50
        self.premium_tier = 1
        self.premium_subscription_count = 5
        self.verification_level = Mock()
        self.verification_level.name = "medium"
        self.mfa_level = 1
        self.features = ["COMMUNITY", "NEWS"]
        self.banner = None
        self.icon = "mock_icon_url"
        self.created_at = datetime.now(timezone.utc)  # Add missing created_at attribute
        self.owner_id = 123456789
        self.afk_channel = None
        self.afk_timeout = 300
        self.system_channel = None
        self.description = f"Mock server: {name}"
        self.large = False
        self.unavailable = False
        self.max_members = 500000
        self.max_presences = None
        self.max_video_channel_users = 25
        self.approximate_member_count = self.member_count
        self.approximate_presence_count = int(self.member_count * 0.3)
        
        # Initialize with default channels and roles
        self.default_role = MockRole("@everyone", permissions=MockPermissions())
        self.me = MockRole("Bot", permissions=MockPermissions(administrator=True))
        
        self.roles = [
            self.default_role,
            MockRole("Admin", permissions=MockPermissions(administrator=True)),
            MockRole("Moderator", permissions=MockPermissions(manage_messages=True, kick_members=True)),
            self.me
        ]
        
        self.categories = [
            MockCategory("General"),
            MockCategory("Voice Channels")
        ]
        
        self.text_channels = [
            MockChannel("general", category=self.categories[0]),
            MockChannel("announcements", category=self.categories[0]),
            MockChannel("random", category=self.categories[0])
        ]
        
        self.voice_channels = [
            MockChannel("General Voice", "voice", category=self.categories[1]),
            MockChannel("Gaming Voice", "voice", category=self.categories[1])
        ]
        
        self.stage_channels = []
        self.emojis = []
        
        # Combine all channels
        self.channels = self.text_channels + self.voice_channels + self.stage_channels + self.categories
        
        # Mock methods
        self.create_category = AsyncMock(side_effect=self._create_category)
        self.create_text_channel = AsyncMock(side_effect=self._create_text_channel)
        self.create_voice_channel = AsyncMock(side_effect=self._create_voice_channel)
        self.create_role = AsyncMock(side_effect=self._create_role)
        self.get_member = Mock(return_value=None)
        
    async def _create_category(self, name: str, **kwargs):
        """Mock category creation."""
        category = MockCategory(name)
        self.categories.append(category)
        self.channels.append(category)
        return category
    
    async def _create_text_channel(self, name: str, category=None, **kwargs):
        """Mock text channel creation."""
        channel = MockChannel(name, "text", category)
        self.text_channels.append(channel)
        self.channels.append(channel)
        return channel
    
    async def _create_voice_channel(self, name: str, category=None, **kwargs):
        """Mock voice channel creation."""
        channel = MockChannel(name, "voice", category)
        self.voice_channels.append(channel)
        self.channels.append(channel)
        return channel
    
    async def _create_role(self, name: str, **kwargs):
        """Mock role creation."""
        permissions = kwargs.get('permissions', MockPermissions())
        role = MockRole(name, permissions=permissions)
        self.roles.append(role)
        return role

class MockMessage:
    """Mock Discord message object."""
    def __init__(self, content: str, author_id: int = 123456, guild: MockGuild = None):
        self.content = content
        self.author = Mock()
        self.author.id = author_id
        self.author.display_name = f"User{author_id}"
        self.guild = guild or MockGuild()
        self.channel = Mock()
        self.channel.typing = AsyncMock()
        self.channel.send = AsyncMock()
        self.edit = AsyncMock()

class MockBot:
    """Mock Discord bot object."""
    def __init__(self):
        self.user = Mock()
        self.user.id = 987654321
        self.guilds = [MockGuild()]
        
def create_test_guild(complexity: str = "simple") -> MockGuild:
    """Create test guilds with different complexity levels."""
    if complexity == "simple":
        return MockGuild("Simple Test Server")
    
    elif complexity == "complex":
        guild = MockGuild("Complex Test Server")
        guild.member_count = 500
        
        # Add more categories
        guild.categories.extend([
            MockCategory("Gaming"),
            MockCategory("Projects"),
            MockCategory("Staff")
        ])
        
        # Add more channels
        guild.text_channels.extend([
            MockChannel("help", category=guild.categories[2]),
            MockChannel("support", category=guild.categories[2]),
            MockChannel("gaming-chat", category=guild.categories[2]),
            MockChannel("project-alpha", category=guild.categories[3]),
            MockChannel("project-beta", category=guild.categories[3])
        ])
        
        # Add more roles
        guild.roles.extend([
            MockRole("VIP", permissions=MockPermissions(manage_messages=True)),
            MockRole("Developer", permissions=MockPermissions(manage_channels=True)),
            MockRole("Gamer"),
            MockRole("Inactive")
        ])

        # Update combined channels list
        guild.channels = guild.text_channels + guild.voice_channels + guild.stage_channels + guild.categories

        return guild
    
    elif complexity == "messy":
        guild = MockGuild("Messy Test Server")
        
        # Create disorganized structure
        guild.text_channels = [
            MockChannel("general"),  # No category
            MockChannel("random"),   # No category
            MockChannel("help"),     # No category
            MockChannel("support"),  # No category
            MockChannel("gaming"),   # No category
            MockChannel("chat"),     # No category
            MockChannel("discussion") # No category
        ]
        
        # Duplicate/similar channels
        guild.text_channels.extend([
            MockChannel("help-desk"),
            MockChannel("support-tickets"),
            MockChannel("general-chat"),
            MockChannel("random-stuff")
        ])
        
        # Update channels list
        guild.channels = guild.text_channels + guild.voice_channels + guild.categories

        return guild
    
    return MockGuild()

# Utility functions for testing
def get_mock_discord_utils():
    """Get mock discord.utils functions."""
    mock_utils = Mock()
    
    def mock_get(collection, **kwargs):
        """Mock discord.utils.get function."""
        if not collection:
            return None
            
        for item in collection:
            match = True
            for key, value in kwargs.items():
                if not hasattr(item, key) or getattr(item, key) != value:
                    match = False
                    break
            if match:
                return item
        return None
    
    mock_utils.get = mock_get
    return mock_utils
