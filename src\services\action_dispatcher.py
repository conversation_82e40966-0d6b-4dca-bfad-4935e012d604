"""
Action dispatcher for executing structured bot actions.
"""
import discord
from typing import Dict, List, Any, Optional, Tuple
from src.utils.logger import bot_logger

class ActionDispatcher:
    """Executes structured actions on Discord servers."""

    def __init__(self, bot):
        self.bot = bot
        # Import services here to avoid circular imports
        from src.services.channel_manager import ChannelManager
        from src.services.role_manager import RoleManager
        from src.services.server_templates import ServerTemplateService

        self.channel_manager = ChannelManager(bot)
        self.role_manager = RoleManager(bot)
        self.template_service = ServerTemplateService(bot)
    
    async def execute_actions(self, actions: List[Dict[str, Any]], guild: discord.Guild) -> Tuple[List[str], List[str]]:
        """
        Execute a list of actions on a guild.
        
        Args:
            actions: List of action dictionaries
            guild: Discord guild to execute actions on
            
        Returns:
            Tuple of (success_messages, error_messages)
        """
        success_messages = []
        error_messages = []
        
        for action in actions:
            try:
                result = await self._execute_single_action(action, guild)
                if result:
                    success_messages.append(result)
            except Exception as e:
                error_msg = f"Failed to execute {action.get('type', 'unknown')}: {str(e)}"
                error_messages.append(error_msg)
                bot_logger.error(f"Action execution error: {e}", exc_info=True)
        
        return success_messages, error_messages
    
    async def _execute_single_action(self, action: Dict[str, Any], guild: discord.Guild) -> Optional[str]:
        """Execute a single action and return success message."""
        action_type = action.get('type')

        # Basic actions
        if action_type == 'create_channel':
            return await self._create_channel(action, guild)
        elif action_type == 'create_category':
            return await self._create_category(action, guild)
        elif action_type == 'delete_channel':
            return await self._delete_channel(action, guild)
        elif action_type == 'delete_category':
            return await self._delete_category(action, guild)
        elif action_type == 'create_role':
            return await self._create_role(action, guild)
        elif action_type == 'delete_role':
            return await self._delete_role(action, guild)
        elif action_type == 'modify_permissions':
            return await self._modify_permissions(action, guild)
        elif action_type == 'analyze_server':
            return await self._analyze_server(action, guild)

        # Advanced channel management actions
        elif action_type == 'create_channel_template':
            template_name = action.get('template_name')
            category_name = action.get('category_name')
            results = await self.channel_manager.create_channel_with_template(guild, template_name, category_name)
            return "\n".join(results)
        elif action_type == 'organize_channels':
            results = await self.channel_manager.organize_channels_by_type(guild)
            return "\n".join(results)
        elif action_type == 'duplicate_channel':
            source = action.get('source_channel')
            target = action.get('target_channel')
            return await self.channel_manager.duplicate_channel(guild, source, target)
        elif action_type == 'bulk_delete_channels':
            pattern = action.get('pattern')
            results = await self.channel_manager.bulk_delete_channels(guild, pattern)
            return "\n".join(results)

        # Advanced role management actions
        elif action_type == 'create_role_hierarchy':
            hierarchy_type = action.get('hierarchy_type')
            results = await self.role_manager.create_role_hierarchy(guild, hierarchy_type)
            return "\n".join(results)
        elif action_type == 'assign_role_colors':
            color_scheme = action.get('color_scheme')
            results = await self.role_manager.assign_role_colors(guild, color_scheme)
            return "\n".join(results)
        elif action_type == 'cleanup_roles':
            results = await self.role_manager.cleanup_unused_roles(guild)
            return "\n".join(results)
        elif action_type == 'organize_role_hierarchy':
            results = await self.role_manager.organize_role_hierarchy(guild)
            return "\n".join(results)
        elif action_type == 'bulk_assign_role':
            role_name = action.get('role_name')
            criteria = action.get('criteria')
            results = await self.role_manager.bulk_assign_role(guild, role_name, criteria)
            return "\n".join(results)

        # Advanced template actions
        elif action_type == 'apply_server_template':
            template_name = action.get('template_name')
            results = await self.template_service.apply_complete_template(guild, template_name)
            return "\n".join(results)

        else:
            raise ValueError(f"Unknown action type: {action_type}")
    
    async def _create_channel(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Create a text or voice channel."""
        name = action.get('name', 'new-channel')
        channel_type = action.get('channel_type', 'text')
        category_name = action.get('category')
        permissions = action.get('permissions', {})
        reason = action.get('reason', 'Bot action')
        
        # Find or create category
        category = None
        if category_name:
            category = discord.utils.get(guild.categories, name=category_name)
            if not category:
                # Create category if it doesn't exist
                category = await guild.create_category(
                    name=category_name,
                    reason=f"Auto-created for channel {name}"
                )
        
        # Build permission overwrites
        overwrites = await self._build_permission_overwrites(permissions, guild)
        
        # Create channel
        if channel_type == 'voice':
            channel = await guild.create_voice_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=reason
            )
            return f"Created voice channel #{channel.name}"
        elif channel_type == 'stage':
            channel = await guild.create_stage_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=reason
            )
            return f"Created stage channel #{channel.name}"
        else:  # text channel
            channel = await guild.create_text_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=reason
            )
            return f"Created text channel #{channel.name}"
    
    async def _create_category(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Create a channel category."""
        name = action.get('name', 'New Category')
        permissions = action.get('permissions', {})
        reason = action.get('reason', 'Bot action')
        
        # Check if category already exists
        existing = discord.utils.get(guild.categories, name=name)
        if existing:
            return f"Category '{name}' already exists"
        
        # Build permission overwrites
        overwrites = await self._build_permission_overwrites(permissions, guild)
        
        # Create category
        category = await guild.create_category(
            name=name,
            overwrites=overwrites,
            reason=reason
        )
        
        return f"Created category '{category.name}'"
    
    async def _delete_channel(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Delete a channel."""
        name = action.get('name')
        reason = action.get('reason', 'Bot action')
        
        if not name:
            raise ValueError("Channel name is required for deletion")
        
        # Find channel
        channel = discord.utils.get(guild.channels, name=name)
        if not channel:
            return f"Channel '{name}' not found"
        
        # Don't delete setup channels
        if self.bot.is_setup_channel(channel.id):
            raise ValueError("Cannot delete setup channel")
        
        channel_type = "voice" if isinstance(channel, discord.VoiceChannel) else "text"
        await channel.delete(reason=reason)
        
        return f"Deleted {channel_type} channel #{name}"
    
    async def _delete_category(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Delete a category and optionally its channels."""
        name = action.get('name')
        delete_channels = action.get('delete_channels', False)
        reason = action.get('reason', 'Bot action')
        
        if not name:
            raise ValueError("Category name is required for deletion")
        
        # Find category
        category = discord.utils.get(guild.categories, name=name)
        if not category:
            return f"Category '{name}' not found"
        
        # Delete channels in category if requested
        deleted_channels = []
        if delete_channels:
            for channel in category.channels:
                if not self.bot.is_setup_channel(channel.id):
                    await channel.delete(reason=reason)
                    deleted_channels.append(channel.name)
        
        # Delete category
        await category.delete(reason=reason)
        
        result = f"Deleted category '{name}'"
        if deleted_channels:
            result += f" and {len(deleted_channels)} channels"
        
        return result
    
    async def _create_role(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Create a role."""
        name = action.get('name', 'New Role')
        color = action.get('color', discord.Color.default())
        permissions = action.get('permissions', {})
        reason = action.get('reason', 'Bot action')
        
        # Check if role already exists
        existing = discord.utils.get(guild.roles, name=name)
        if existing:
            return f"Role '{name}' already exists"
        
        # Create role
        role = await guild.create_role(
            name=name,
            color=color,
            reason=reason
        )
        
        return f"Created role '{role.name}'"
    
    async def _delete_role(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Delete a role."""
        name = action.get('name')
        reason = action.get('reason', 'Bot action')
        
        if not name:
            raise ValueError("Role name is required for deletion")
        
        # Find role
        role = discord.utils.get(guild.roles, name=name)
        if not role:
            return f"Role '{name}' not found"
        
        # Don't delete admin roles or @everyone
        if role.permissions.administrator or role == guild.default_role:
            raise ValueError("Cannot delete administrator roles or @everyone")
        
        await role.delete(reason=reason)
        return f"Deleted role '{name}'"
    
    async def _modify_permissions(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Modify channel or role permissions."""
        # This is a complex action that would need more specific implementation
        # For now, return a placeholder
        return "Permission modification not yet implemented"
    
    async def _analyze_server(self, action: Dict[str, Any], guild: discord.Guild) -> str:
        """Handle server analysis action."""
        analysis = action.get('analysis', 'No analysis provided')
        missing_elements = action.get('missing_elements', [])
        recommendations = action.get('recommendations', [])
        
        # Format the analysis for display
        result = f"**Server Analysis:**\n{analysis}\n\n"
        
        if missing_elements:
            result += f"**Missing Elements:**\n" + "\n".join(f"• {item}" for item in missing_elements) + "\n\n"
        
        if recommendations:
            result += f"**Recommendations:**\n" + "\n".join(f"• {rec}" for rec in recommendations)
        
        return result
    
    async def _build_permission_overwrites(self, permissions: Dict[str, List[str]], guild: discord.Guild) -> Dict:
        """Build Discord permission overwrites from permission dictionary."""
        overwrites = {}
        
        for role_name, perms in permissions.items():
            if role_name == "@everyone":
                target = guild.default_role
            else:
                target = discord.utils.get(guild.roles, name=role_name)
                if not target:
                    # Create role if it doesn't exist
                    target = await guild.create_role(name=role_name, reason="Auto-created for permissions")
            
            # Convert permission strings to Discord permissions
            perm_dict = {}
            for perm in perms:
                if hasattr(discord.Permissions, perm):
                    perm_dict[perm] = True
            
            if perm_dict:
                overwrites[target] = discord.PermissionOverwrite(**perm_dict)
        
        return overwrites
