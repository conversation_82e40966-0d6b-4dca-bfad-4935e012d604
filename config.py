"""
Configuration settings for the Discord bot.
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Discord Configuration
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
if not DISCORD_TOKEN:
    raise ValueError("DISCORD_TOKEN not found in environment variables")

# Groq AI Configuration
GROQ_API_KEY = os.getenv('GROQ_API_KEY')
if not GROQ_API_KEY:
    raise ValueError("GROQ_API_KEY not found in environment variables")

# Bot Configuration
BOT_PREFIX = '!'
SETUP_CHANNEL_NAME = 'server-setup'
PERSISTENCE_FILE = 'bot_data.json'
LOG_FILE = 'bot.log'

# Permissions
SETUP_CHANNEL_PERMISSIONS = {
    'view_channel': True,
    'send_messages': True,
    'read_message_history': True
}

# Logging Configuration
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'
