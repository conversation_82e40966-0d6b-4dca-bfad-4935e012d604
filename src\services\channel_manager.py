"""
Advanced channel management service for Discord servers.
"""
import discord
from typing import Dict, List, Optional, Tuple
from src.utils.logger import bot_logger

class ChannelManager:
    """Advanced channel management operations."""
    
    def __init__(self, bot):
        self.bot = bot
    
    async def create_channel_with_template(self, guild: discord.Guild, template_name: str, 
                                         category_name: Optional[str] = None) -> List[str]:
        """Create channels based on predefined templates."""
        templates = {
            "gaming_community": [
                {"name": "welcome", "type": "text", "topic": "Welcome new members!", "permissions": {"@everyone": ["view_channel"], "Member": ["view_channel"]}},
                {"name": "rules", "type": "text", "topic": "Server rules and guidelines", "permissions": {"@everyone": ["view_channel"], "Moderator": ["view_channel", "send_messages"]}},
                {"name": "announcements", "type": "text", "topic": "Important server announcements", "permissions": {"@everyone": ["view_channel"], "Admin": ["view_channel", "send_messages"]}},
                {"name": "general-chat", "type": "text", "topic": "General discussion", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "gaming-discussion", "type": "text", "topic": "Discuss your favorite games", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "General Voice", "type": "voice", "permissions": {"@everyone": ["view_channel", "connect", "speak"]}},
                {"name": "Gaming Voice", "type": "voice", "permissions": {"@everyone": ["view_channel", "connect", "speak"]}}
            ],
            "professional_workspace": [
                {"name": "welcome", "type": "text", "topic": "Welcome to the workspace", "permissions": {"@everyone": ["view_channel"], "Member": ["view_channel"]}},
                {"name": "announcements", "type": "text", "topic": "Company announcements", "permissions": {"@everyone": ["view_channel"], "Management": ["view_channel", "send_messages"]}},
                {"name": "general", "type": "text", "topic": "General workplace discussion", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "projects", "type": "text", "topic": "Project coordination", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "resources", "type": "text", "topic": "Shared resources and links", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "Meeting Room 1", "type": "voice", "permissions": {"@everyone": ["view_channel", "connect", "speak"]}},
                {"name": "Meeting Room 2", "type": "voice", "permissions": {"@everyone": ["view_channel", "connect", "speak"]}}
            ],
            "study_group": [
                {"name": "welcome", "type": "text", "topic": "Welcome to the study group", "permissions": {"@everyone": ["view_channel"], "Student": ["view_channel"]}},
                {"name": "general-discussion", "type": "text", "topic": "General study discussion", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "homework-help", "type": "text", "topic": "Get help with homework", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "resources", "type": "text", "topic": "Study materials and resources", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                {"name": "Study Session", "type": "voice", "permissions": {"@everyone": ["view_channel", "connect", "speak"]}},
                {"name": "Quiet Study", "type": "voice", "permissions": {"@everyone": ["view_channel", "connect"]}}
            ]
        }
        
        if template_name not in templates:
            raise ValueError(f"Unknown template: {template_name}")
        
        template = templates[template_name]
        results = []
        
        # Create category if specified
        category = None
        if category_name:
            category = await self._get_or_create_category(guild, category_name)
        
        # Create channels from template
        for channel_config in template:
            try:
                channel = await self._create_channel_from_config(guild, channel_config, category)
                results.append(f"Created {channel_config['type']} channel #{channel.name}")
            except Exception as e:
                bot_logger.error(f"Failed to create channel {channel_config['name']}: {e}")
                results.append(f"Failed to create {channel_config['name']}: {str(e)}")
        
        return results
    
    async def organize_channels_by_type(self, guild: discord.Guild) -> List[str]:
        """Organize existing channels into logical categories."""
        results = []
        
        # Define category mappings
        category_mappings = {
            "Information": ["welcome", "rules", "announcements", "info"],
            "General": ["general", "chat", "discussion", "lounge"],
            "Gaming": ["gaming", "game", "minecraft", "valorant", "league"],
            "Voice Channels": [],  # Will be populated with voice channels
            "Moderation": ["mod", "admin", "staff", "reports"],
            "Miscellaneous": []  # Catch-all for unmatched channels
        }
        
        # Get uncategorized channels
        uncategorized_channels = [ch for ch in guild.channels 
                                if ch.category is None and not isinstance(ch, discord.CategoryChannel)]
        
        if not uncategorized_channels:
            return ["No uncategorized channels found"]
        
        # Create categories and move channels
        for category_name, keywords in category_mappings.items():
            matching_channels = []
            
            if category_name == "Voice Channels":
                # Add all voice channels
                matching_channels = [ch for ch in uncategorized_channels 
                                   if isinstance(ch, (discord.VoiceChannel, discord.StageChannel))]
            else:
                # Match by keywords
                for channel in uncategorized_channels:
                    if isinstance(channel, discord.TextChannel):
                        channel_name_lower = channel.name.lower()
                        if any(keyword in channel_name_lower for keyword in keywords):
                            matching_channels.append(channel)
            
            if matching_channels:
                # Create category
                category = await self._get_or_create_category(guild, category_name)
                
                # Move channels
                for channel in matching_channels:
                    try:
                        await channel.edit(category=category, reason="Channel organization")
                        results.append(f"Moved #{channel.name} to {category_name}")
                        uncategorized_channels.remove(channel)
                    except Exception as e:
                        bot_logger.error(f"Failed to move channel {channel.name}: {e}")
        
        # Move remaining channels to Miscellaneous
        if uncategorized_channels:
            misc_category = await self._get_or_create_category(guild, "Miscellaneous")
            for channel in uncategorized_channels:
                try:
                    await channel.edit(category=misc_category, reason="Channel organization")
                    results.append(f"Moved #{channel.name} to Miscellaneous")
                except Exception as e:
                    bot_logger.error(f"Failed to move channel {channel.name}: {e}")
        
        return results if results else ["No channels needed organization"]
    
    async def duplicate_channel(self, guild: discord.Guild, source_channel_name: str, 
                              new_channel_name: str) -> str:
        """Duplicate a channel with all its settings."""
        source_channel = discord.utils.get(guild.channels, name=source_channel_name)
        if not source_channel:
            raise ValueError(f"Source channel '{source_channel_name}' not found")
        
        # Prepare channel creation parameters
        kwargs = {
            "name": new_channel_name,
            "category": source_channel.category,
            "overwrites": source_channel.overwrites,
            "reason": f"Duplicated from #{source_channel.name}"
        }
        
        # Add channel-specific parameters
        if isinstance(source_channel, discord.TextChannel):
            kwargs.update({
                "topic": source_channel.topic,
                "slowmode_delay": source_channel.slowmode_delay,
                "nsfw": source_channel.nsfw
            })
            new_channel = await guild.create_text_channel(**kwargs)
        elif isinstance(source_channel, discord.VoiceChannel):
            kwargs.update({
                "bitrate": source_channel.bitrate,
                "user_limit": source_channel.user_limit
            })
            new_channel = await guild.create_voice_channel(**kwargs)
        elif isinstance(source_channel, discord.StageChannel):
            new_channel = await guild.create_stage_channel(**kwargs)
        else:
            raise ValueError(f"Unsupported channel type: {type(source_channel)}")
        
        return f"Duplicated #{source_channel.name} as #{new_channel.name}"
    
    async def bulk_delete_channels(self, guild: discord.Guild, pattern: str) -> List[str]:
        """Delete multiple channels matching a pattern."""
        results = []
        channels_to_delete = []
        
        # Find matching channels
        for channel in guild.channels:
            if (not isinstance(channel, discord.CategoryChannel) and 
                pattern.lower() in channel.name.lower() and
                not self.bot.is_setup_channel(channel.id)):
                channels_to_delete.append(channel)
        
        if not channels_to_delete:
            return [f"No channels found matching pattern '{pattern}'"]
        
        # Delete channels
        for channel in channels_to_delete:
            try:
                await channel.delete(reason=f"Bulk delete - pattern: {pattern}")
                results.append(f"Deleted #{channel.name}")
            except Exception as e:
                bot_logger.error(f"Failed to delete channel {channel.name}: {e}")
                results.append(f"Failed to delete #{channel.name}: {str(e)}")
        
        return results
    
    async def _get_or_create_category(self, guild: discord.Guild, name: str) -> discord.CategoryChannel:
        """Get existing category or create new one."""
        category = discord.utils.get(guild.categories, name=name)
        if not category:
            category = await guild.create_category(name=name, reason="Auto-created for organization")
            bot_logger.info(f"Created category '{name}' in {guild.name}")
        return category
    
    async def _create_channel_from_config(self, guild: discord.Guild, config: Dict, 
                                        category: Optional[discord.CategoryChannel] = None) -> discord.abc.GuildChannel:
        """Create a channel from configuration dictionary."""
        name = config["name"]
        channel_type = config.get("type", "text")
        topic = config.get("topic")
        permissions = config.get("permissions", {})
        
        # Build permission overwrites
        overwrites = {}
        for role_name, perms in permissions.items():
            if role_name == "@everyone":
                target = guild.default_role
            else:
                target = discord.utils.get(guild.roles, name=role_name)
                if not target:
                    # Create role if it doesn't exist
                    target = await guild.create_role(name=role_name, reason="Auto-created for channel permissions")
            
            # Convert permission strings to Discord permissions
            perm_dict = {}
            for perm in perms:
                if hasattr(discord.Permissions, perm):
                    perm_dict[perm] = True
            
            if perm_dict:
                overwrites[target] = discord.PermissionOverwrite(**perm_dict)
        
        # Create channel based on type
        if channel_type == "voice":
            return await guild.create_voice_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason="Created from template"
            )
        elif channel_type == "stage":
            return await guild.create_stage_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason="Created from template"
            )
        else:  # text channel
            return await guild.create_text_channel(
                name=name,
                category=category,
                topic=topic,
                overwrites=overwrites,
                reason="Created from template"
            )
