"""
Main Discord bot class with initialization and core functionality.
"""
import discord
from discord.ext import commands
from typing import Dict, Optional
import config
from src.utils.logger import bot_logger
from src.utils.persistence import persistence_manager

class DiscordServerBot(commands.Bot):
    """Main Discord bot class for server management."""
    
    def __init__(self):
        # Configure bot intents
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        intents.guild_messages = True
        intents.members = True
        
        super().__init__(
            command_prefix=config.BOT_PREFIX,
            intents=intents,
            help_command=None  # We'll implement custom help
        )
        
        # Store setup channels for quick access
        self.setup_channels: Dict[int, int] = {}  # server_id -> channel_id
        
    async def setup_hook(self):
        """Called when the bot is starting up."""
        bot_logger.info("Bot is starting up...")
        
        # Load persistent data
        await self.load_setup_channels()
        
        bot_logger.info(f"Bot setup complete. Loaded {len(self.setup_channels)} setup channels.")
    
    async def load_setup_channels(self):
        """Load setup channels from persistence."""
        try:
            channels_data = await persistence_manager.get_setup_channels()
            # Convert string keys back to integers
            self.setup_channels = {int(k): v for k, v in channels_data.items()}
            bot_logger.info(f"Loaded {len(self.setup_channels)} setup channels from persistence")
        except Exception as e:
            bot_logger.error(f"Error loading setup channels: {e}", exc_info=True)
            self.setup_channels = {}
    
    async def on_ready(self):
        """Called when the bot has successfully connected to Discord."""
        bot_logger.info(f"Bot is ready! Logged in as {self.user} (ID: {self.user.id})")
        bot_logger.info(f"Connected to {len(self.guilds)} servers")
        
        # Verify setup channels still exist
        await self.verify_setup_channels()
        
        # Set bot status
        await self.change_presence(
            activity=discord.Activity(
                type=discord.ActivityType.watching,
                name="for !setup commands"
            )
        )
    
    async def verify_setup_channels(self):
        """Verify that all stored setup channels still exist and are accessible."""
        invalid_channels = []
        
        for server_id, channel_id in self.setup_channels.items():
            guild = self.get_guild(server_id)
            if not guild:
                bot_logger.warning(f"Bot no longer in server {server_id}, removing setup channel")
                invalid_channels.append(server_id)
                continue
            
            channel = guild.get_channel(channel_id)
            if not channel:
                bot_logger.warning(f"Setup channel {channel_id} no longer exists in server {server_id}")
                invalid_channels.append(server_id)
                continue
            
            bot_logger.debug(f"Verified setup channel #{channel.name} in {guild.name}")
        
        # Remove invalid channels
        for server_id in invalid_channels:
            await self.remove_setup_channel(server_id)
    
    async def add_setup_channel(self, server_id: int, channel_id: int):
        """Add a setup channel mapping."""
        self.setup_channels[server_id] = channel_id
        await persistence_manager.add_setup_channel(server_id, channel_id)
    
    async def remove_setup_channel(self, server_id: int):
        """Remove a setup channel mapping."""
        if server_id in self.setup_channels:
            del self.setup_channels[server_id]
        await persistence_manager.remove_setup_channel(server_id)
    
    def is_setup_channel(self, channel_id: int) -> bool:
        """Check if a channel is a setup channel."""
        return channel_id in self.setup_channels.values()
    
    def get_setup_channel_id(self, server_id: int) -> Optional[int]:
        """Get the setup channel ID for a server."""
        return self.setup_channels.get(server_id)
    
    async def on_guild_remove(self, guild):
        """Called when the bot is removed from a guild."""
        bot_logger.info(f"Bot removed from guild: {guild.name} (ID: {guild.id})")
        await self.remove_setup_channel(guild.id)
    
    async def on_message(self, message):
        """Handle incoming messages."""
        # Ignore bot messages
        if message.author.bot:
            return
        
        # Only process commands in setup channels or if it's the setup command
        if not self.is_setup_channel(message.channel.id) and not message.content.startswith(f"{config.BOT_PREFIX}setup"):
            return
        
        # Process commands
        await self.process_commands(message)
    
    async def on_command_error(self, ctx, error):
        """Handle command errors."""
        if isinstance(error, commands.CommandNotFound):
            # For setup channels, we'll handle unknown commands with AI
            if self.is_setup_channel(ctx.channel.id):
                # This will be handled by the AI system later
                pass
            return
        
        bot_logger.error(f"Command error in {ctx.guild.name if ctx.guild else 'DM'}: {error}", exc_info=True)
        
        try:
            await ctx.send("❌ An error occurred while processing your command. Please try again.")
        except:
            pass  # Channel might be deleted or bot lacks permissions
