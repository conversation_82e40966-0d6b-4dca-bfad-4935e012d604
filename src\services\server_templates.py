"""
Advanced server templates and design patterns.
"""
import discord
from typing import Dict, List, Any, Optional
from src.utils.logger import bot_logger

class ServerTemplateService:
    """Service for applying comprehensive server templates."""
    
    def __init__(self, bot):
        self.bot = bot
    
    async def apply_complete_template(self, guild: discord.Guild, template_name: str) -> List[str]:
        """Apply a complete server template including roles, channels, and permissions."""
        templates = {
            "gaming_community": {
                "description": "Complete gaming community setup with roles, channels, and organization",
                "roles": [
                    {"name": "Owner", "color": "red", "permissions": ["administrator"], "hoist": True},
                    {"name": "Admin", "color": "orange", "permissions": ["manage_guild", "manage_channels", "manage_roles", "kick_members", "ban_members"], "hoist": True},
                    {"name": "Moderator", "color": "blue", "permissions": ["manage_messages", "kick_members"], "hoist": True},
                    {"name": "VIP", "color": "gold", "permissions": [], "hoist": True},
                    {"name": "Member", "color": "green", "permissions": [], "hoist": False},
                    {"name": "New Member", "color": "light_grey", "permissions": [], "hoist": False}
                ],
                "categories": [
                    {
                        "name": "📋 Information",
                        "channels": [
                            {"name": "welcome", "type": "text", "topic": "Welcome to our gaming community!", "permissions": {"@everyone": ["view_channel"], "Member": ["view_channel"]}},
                            {"name": "rules", "type": "text", "topic": "Server rules and guidelines", "permissions": {"@everyone": ["view_channel"], "Moderator": ["view_channel", "send_messages"]}},
                            {"name": "announcements", "type": "text", "topic": "Important server announcements", "permissions": {"@everyone": ["view_channel"], "Admin": ["view_channel", "send_messages"]}}
                        ]
                    },
                    {
                        "name": "💬 General",
                        "channels": [
                            {"name": "general-chat", "type": "text", "topic": "General discussion for everyone", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                            {"name": "introductions", "type": "text", "topic": "Introduce yourself to the community", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                            {"name": "off-topic", "type": "text", "topic": "Random discussions and fun", "permissions": {"Member": ["view_channel", "send_messages"]}}
                        ]
                    },
                    {
                        "name": "🎮 Gaming",
                        "channels": [
                            {"name": "gaming-discussion", "type": "text", "topic": "Discuss your favorite games", "permissions": {"@everyone": ["view_channel", "send_messages"]}},
                            {"name": "looking-for-group", "type": "text", "topic": "Find teammates and gaming partners", "permissions": {"Member": ["view_channel", "send_messages"]}},
                            {"name": "game-updates", "type": "text", "topic": "Latest gaming news and updates", "permissions": {"@everyone": ["view_channel"], "Moderator": ["view_channel", "send_messages"]}}
                        ]
                    },
                    {
                        "name": "🔊 Voice Channels",
                        "channels": [
                            {"name": "General Hangout", "type": "voice", "permissions": {"@everyone": ["view_channel", "connect", "speak"]}},
                            {"name": "Gaming Session 1", "type": "voice", "permissions": {"Member": ["view_channel", "connect", "speak"]}},
                            {"name": "Gaming Session 2", "type": "voice", "permissions": {"Member": ["view_channel", "connect", "speak"]}},
                            {"name": "VIP Lounge", "type": "voice", "permissions": {"VIP": ["view_channel", "connect", "speak"], "Admin": ["view_channel", "connect", "speak"]}}
                        ]
                    }
                ]
            },
            "professional_workspace": {
                "description": "Professional workspace for teams and organizations",
                "roles": [
                    {"name": "CEO", "color": "red", "permissions": ["administrator"], "hoist": True},
                    {"name": "Management", "color": "orange", "permissions": ["manage_guild", "manage_channels", "manage_roles"], "hoist": True},
                    {"name": "Team Lead", "color": "blue", "permissions": ["manage_messages"], "hoist": True},
                    {"name": "Employee", "color": "green", "permissions": [], "hoist": False},
                    {"name": "Contractor", "color": "purple", "permissions": [], "hoist": False},
                    {"name": "Guest", "color": "light_grey", "permissions": [], "hoist": False}
                ],
                "categories": [
                    {
                        "name": "📢 Company",
                        "channels": [
                            {"name": "announcements", "type": "text", "topic": "Company-wide announcements", "permissions": {"@everyone": ["view_channel"], "Management": ["view_channel", "send_messages"]}},
                            {"name": "general", "type": "text", "topic": "General workplace discussion", "permissions": {"Employee": ["view_channel", "send_messages"], "Contractor": ["view_channel", "send_messages"]}},
                            {"name": "resources", "type": "text", "topic": "Shared resources and documentation", "permissions": {"Employee": ["view_channel", "send_messages"], "Contractor": ["view_channel"]}}
                        ]
                    },
                    {
                        "name": "💼 Projects",
                        "channels": [
                            {"name": "project-coordination", "type": "text", "topic": "Coordinate ongoing projects", "permissions": {"Team Lead": ["view_channel", "send_messages"], "Employee": ["view_channel", "send_messages"]}},
                            {"name": "development", "type": "text", "topic": "Development discussions", "permissions": {"Employee": ["view_channel", "send_messages"]}},
                            {"name": "design", "type": "text", "topic": "Design and creative work", "permissions": {"Employee": ["view_channel", "send_messages"]}}
                        ]
                    },
                    {
                        "name": "🎯 Meetings",
                        "channels": [
                            {"name": "Conference Room A", "type": "voice", "permissions": {"Employee": ["view_channel", "connect", "speak"], "Contractor": ["view_channel", "connect", "speak"]}},
                            {"name": "Conference Room B", "type": "voice", "permissions": {"Employee": ["view_channel", "connect", "speak"], "Contractor": ["view_channel", "connect", "speak"]}},
                            {"name": "Executive Meeting", "type": "voice", "permissions": {"Management": ["view_channel", "connect", "speak"]}}
                        ]
                    }
                ]
            },
            "educational": {
                "description": "Educational environment for schools and study groups",
                "roles": [
                    {"name": "Principal", "color": "red", "permissions": ["administrator"], "hoist": True},
                    {"name": "Teacher", "color": "orange", "permissions": ["manage_channels", "manage_messages", "kick_members"], "hoist": True},
                    {"name": "Teaching Assistant", "color": "blue", "permissions": ["manage_messages"], "hoist": True},
                    {"name": "Student", "color": "green", "permissions": [], "hoist": False},
                    {"name": "Parent", "color": "purple", "permissions": [], "hoist": False},
                    {"name": "Visitor", "color": "light_grey", "permissions": [], "hoist": False}
                ],
                "categories": [
                    {
                        "name": "📚 General",
                        "channels": [
                            {"name": "announcements", "type": "text", "topic": "School announcements", "permissions": {"@everyone": ["view_channel"], "Teacher": ["view_channel", "send_messages"]}},
                            {"name": "general-discussion", "type": "text", "topic": "General school discussion", "permissions": {"Student": ["view_channel", "send_messages"], "Teacher": ["view_channel", "send_messages"]}},
                            {"name": "resources", "type": "text", "topic": "Educational resources and materials", "permissions": {"Student": ["view_channel"], "Teacher": ["view_channel", "send_messages"]}}
                        ]
                    },
                    {
                        "name": "📖 Subjects",
                        "channels": [
                            {"name": "mathematics", "type": "text", "topic": "Math discussions and homework help", "permissions": {"Student": ["view_channel", "send_messages"], "Teacher": ["view_channel", "send_messages"]}},
                            {"name": "science", "type": "text", "topic": "Science discussions and experiments", "permissions": {"Student": ["view_channel", "send_messages"], "Teacher": ["view_channel", "send_messages"]}},
                            {"name": "literature", "type": "text", "topic": "Literature and writing discussions", "permissions": {"Student": ["view_channel", "send_messages"], "Teacher": ["view_channel", "send_messages"]}}
                        ]
                    },
                    {
                        "name": "🎓 Study Rooms",
                        "channels": [
                            {"name": "Study Hall", "type": "voice", "permissions": {"Student": ["view_channel", "connect", "speak"], "Teacher": ["view_channel", "connect", "speak"]}},
                            {"name": "Group Study", "type": "voice", "permissions": {"Student": ["view_channel", "connect", "speak"]}},
                            {"name": "Teacher Office Hours", "type": "voice", "permissions": {"Student": ["view_channel", "connect", "speak"], "Teacher": ["view_channel", "connect", "speak"]}}
                        ]
                    }
                ]
            }
        }
        
        if template_name not in templates:
            raise ValueError(f"Unknown template: {template_name}")
        
        template = templates[template_name]
        results = []
        
        try:
            # Create roles first
            results.append(f"🎭 Creating roles for {template['description']}...")
            for role_config in reversed(template["roles"]):  # Create in reverse order for hierarchy
                existing_role = discord.utils.get(guild.roles, name=role_config["name"])
                if existing_role:
                    results.append(f"Role '{role_config['name']}' already exists")
                    continue
                
                # Convert color string to Discord color
                color = getattr(discord.Color, role_config.get("color", "default"))()
                
                # Build permissions
                permissions = discord.Permissions()
                for perm_name in role_config.get("permissions", []):
                    if hasattr(permissions, perm_name):
                        setattr(permissions, perm_name, True)
                
                role = await guild.create_role(
                    name=role_config["name"],
                    color=color,
                    permissions=permissions,
                    hoist=role_config.get("hoist", False),
                    reason=f"Template: {template_name}"
                )
                results.append(f"✅ Created role '{role.name}'")
            
            # Create categories and channels
            results.append(f"📁 Creating categories and channels...")
            for category_config in template["categories"]:
                # Create category
                category = await guild.create_category(
                    name=category_config["name"],
                    reason=f"Template: {template_name}"
                )
                results.append(f"✅ Created category '{category.name}'")
                
                # Create channels in category
                for channel_config in category_config["channels"]:
                    channel = await self._create_channel_from_config(guild, channel_config, category, template_name)
                    results.append(f"✅ Created {channel_config['type']} channel #{channel.name}")
            
            results.append(f"🎉 Successfully applied '{template_name}' template!")
            
        except Exception as e:
            bot_logger.error(f"Error applying template {template_name}: {e}", exc_info=True)
            results.append(f"❌ Error applying template: {str(e)}")
        
        return results
    
    async def _create_channel_from_config(self, guild: discord.Guild, config: Dict, 
                                        category: discord.CategoryChannel, template_name: str) -> discord.abc.GuildChannel:
        """Create a channel from configuration with proper permissions."""
        name = config["name"]
        channel_type = config.get("type", "text")
        topic = config.get("topic")
        permissions = config.get("permissions", {})
        
        # Build permission overwrites
        overwrites = {}
        for role_name, perms in permissions.items():
            if role_name == "@everyone":
                target = guild.default_role
            else:
                target = discord.utils.get(guild.roles, name=role_name)
                if not target:
                    bot_logger.warning(f"Role '{role_name}' not found for channel permissions")
                    continue
            
            # Convert permission strings to Discord permissions
            perm_dict = {}
            for perm in perms:
                if hasattr(discord.Permissions, perm):
                    perm_dict[perm] = True
            
            if perm_dict:
                overwrites[target] = discord.PermissionOverwrite(**perm_dict)
        
        # Create channel based on type
        if channel_type == "voice":
            return await guild.create_voice_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=f"Template: {template_name}"
            )
        elif channel_type == "stage":
            return await guild.create_stage_channel(
                name=name,
                category=category,
                overwrites=overwrites,
                reason=f"Template: {template_name}"
            )
        else:  # text channel
            return await guild.create_text_channel(
                name=name,
                category=category,
                topic=topic,
                overwrites=overwrites,
                reason=f"Template: {template_name}"
            )
