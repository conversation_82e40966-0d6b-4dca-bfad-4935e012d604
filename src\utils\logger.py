"""
Comprehensive logging system for the Discord bot.
Enhanced with multi-step operation tracking and detailed execution sequences.
"""
import logging
import os
import json
from datetime import datetime
from typing import Optional, Dict, List, Any
import config

class BotLogger:
    """Centralized logging system for bot operations."""
    
    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """Configure logging with both file and console handlers."""
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger('discord_bot')
        self.logger.setLevel(getattr(logging, config.LOG_LEVEL))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(config.LOG_FORMAT)
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(f'logs/{config.LOG_FILE}', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        
        # Console handler for important messages
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def log_command(self, server_id: int, server_name: str, user: str, command: str, 
                   intent: Optional[str] = None, actions_count: int = 0, 
                   status: str = "PROCESSING", result: Optional[str] = None):
        """Log a comprehensive command processing entry."""
        log_entry = f"""
=== BOT PROCESSING LOG ===
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Server: {server_name} (ID: {server_id})
User: {user}
Input: "{command}"
Parsed Intent: {intent or 'N/A'}
Generated Actions: {actions_count}
Execution Status: {status}
Result: {result or 'N/A'}
=== END LOG ===
        """.strip()
        
        self.logger.info(log_entry)
    
    def info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message."""
        self.logger.warning(message)
    
    def error(self, message: str, exc_info: bool = False):
        """Log error message."""
        self.logger.error(message, exc_info=exc_info)
    
    def debug(self, message: str):
        """Log debug message."""
        self.logger.debug(message)

    def log_multi_step_operation(
        self,
        user_id: int,
        guild_id: int,
        guild_name: str,
        user_request: str,
        parsed_intents: List[str],
        execution_sequence: List[Dict[str, Any]],
        execution_summary: Dict[str, Any]
    ):
        """Log comprehensive multi-step operation details."""
        timestamp = datetime.now().isoformat()

        # Log structured header
        self.logger.info("=" * 80)
        self.logger.info("MULTI-STEP OPERATION LOG")
        self.logger.info(f"Timestamp: {timestamp}")
        self.logger.info(f"Server: {guild_name} (ID: {guild_id})")
        self.logger.info(f"User: {user_id}")
        self.logger.info(f"Original Request: \"{user_request}\"")
        self.logger.info(f"Parsed Intents: {parsed_intents}")
        self.logger.info(f"Planned Actions: {len(execution_sequence)}")

        # Log execution sequence
        self.logger.info("\nEXECUTION SEQUENCE:")
        for i, step in enumerate(execution_sequence, 1):
            status = step.get('status', 'unknown')
            action_type = step.get('action_type', 'unknown')
            result = step.get('result', 'No result')
            error = step.get('error', '')

            status_symbol = "✅" if status == "completed" else "❌" if status == "failed" else "⏳"
            self.logger.info(f"Step {i}: {action_type} - {status_symbol} {status}")

            if status == "completed":
                self.logger.info(f"  Result: {result}")
            elif status == "failed" and error:
                self.logger.info(f"  Error: {error}")

        # Log final summary
        self.logger.info("\nFINAL SUMMARY:")
        total_actions = execution_summary.get('total_actions', 0)
        completed = execution_summary.get('completed_actions', 0)
        failed = execution_summary.get('failed_actions', 0)
        execution_time = execution_summary.get('execution_time', 0.0)
        rollback_performed = execution_summary.get('rollback_performed', False)

        self.logger.info(f"Success Rate: {completed}/{total_actions} actions completed")
        if failed > 0:
            self.logger.info(f"Failed Actions: {failed}")
        if rollback_performed:
            self.logger.info("Rollback: Performed due to critical failures")
        self.logger.info(f"Execution Time: {execution_time:.2f} seconds")
        self.logger.info("=" * 80)

    def log_ai_parsing(self, user_request: str, parsing_time: float, intents_detected: int,
                      actions_generated: int, success: bool):
        """Log AI parsing performance and results."""
        status = "SUCCESS" if success else "FAILED"

        self.logger.info(f"=== AI PARSING LOG ===")
        self.logger.info(f"Request: {user_request[:100]}{'...' if len(user_request) > 100 else ''}")
        self.logger.info(f"Parsing Time: {parsing_time:.3f}s")
        self.logger.info(f"Intents Detected: {intents_detected}")
        self.logger.info(f"Actions Generated: {actions_generated}")
        self.logger.info(f"Status: {status}")
        self.logger.info("=" * 40)

# Global logger instance
bot_logger = BotLogger()
