"""
Comprehensive logging system for the Discord bot.
"""
import logging
import os
from datetime import datetime
from typing import Optional
import config

class BotLogger:
    """Centralized logging system for bot operations."""
    
    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """Configure logging with both file and console handlers."""
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger('discord_bot')
        self.logger.setLevel(getattr(logging, config.LOG_LEVEL))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(config.LOG_FORMAT)
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(f'logs/{config.LOG_FILE}', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        
        # Console handler for important messages
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def log_command(self, server_id: int, server_name: str, user: str, command: str, 
                   intent: Optional[str] = None, actions_count: int = 0, 
                   status: str = "PROCESSING", result: Optional[str] = None):
        """Log a comprehensive command processing entry."""
        log_entry = f"""
=== BOT PROCESSING LOG ===
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Server: {server_name} (ID: {server_id})
User: {user}
Input: "{command}"
Parsed Intent: {intent or 'N/A'}
Generated Actions: {actions_count}
Execution Status: {status}
Result: {result or 'N/A'}
=== END LOG ===
        """.strip()
        
        self.logger.info(log_entry)
    
    def info(self, message: str):
        """Log info message."""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message."""
        self.logger.warning(message)
    
    def error(self, message: str, exc_info: bool = False):
        """Log error message."""
        self.logger.error(message, exc_info=exc_info)
    
    def debug(self, message: str):
        """Log debug message."""
        self.logger.debug(message)

# Global logger instance
bot_logger = BotLogger()
