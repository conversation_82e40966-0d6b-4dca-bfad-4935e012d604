# Discord Server Management Bot

An intelligent Discord bot that enables server administrators to manage their Discord servers through natural language commands using AI.

## 🌟 Features

### Core Functionality
- **Natural Language Processing**: Use everyday language to manage your server
- **AI-Powered Intent Recognition**: Powered by Gemini AI for understanding complex commands
- **Comprehensive Logging**: Detailed logs of all bot actions and user interactions
- **Persistent Setup**: <PERSON><PERSON> remembers setup channels across restarts
- **Administrator-Only Access**: Secure setup channels visible only to administrators

### Server Management Capabilities

#### Basic Operations
- Create/delete text, voice, and stage channels
- Create/delete channel categories
- Create/delete roles with custom permissions
- Modify channel and role permissions
- Server structure analysis and recommendations

#### Advanced Features
- **Complete Server Templates**: Apply full server layouts for different purposes
  - Gaming Community
  - Professional Workspace  
  - Educational Environment
- **Smart Organization**: Automatically organize channels into logical categories
- **Role Management**: Create role hierarchies with appropriate permissions and colors
- **Bulk Operations**: Delete multiple channels, assign roles to groups of users
- **Channel Duplication**: Copy channels with all settings intact

#### AI Commands Examples
```
"Design a server for my gaming community"
"Create a support channel in a Help category"
"Delete all channels with 'test' in the name"
"Organize my messy server structure"
"Set up a professional workspace"
"What's missing from this server?"
"Remove the announcements channel and create an updates channel instead"
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Discord Bot Token
- Gemini API Key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd discord-server-bot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   - Copy `.env.example` to `.env`
   - Add your Discord bot token and Gemini API key:
   ```
   DISCORD_TOKEN=your_discord_bot_token_here
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

4. **Run the bot**
   ```bash
   python main.py
   ```

### Discord Setup

1. **Invite the bot to your server** with the following permissions:
   - Manage Channels
   - Manage Roles
   - Send Messages
   - Read Message History
   - View Channels

2. **Initialize the bot** in your server:
   ```
   !setup
   ```

3. **Start using natural language commands** in the created `server-setup` channel!

## 📋 Command Reference

### Setup Commands
- `!setup` - Initialize the bot and create the setup channel
- `!clear` - Reset the server (removes all channels/roles except setup channel and admin roles)

### Natural Language Commands
Use these in the `server-setup` channel:

#### Server Design
- "Design a server for [purpose]"
- "Set up a [type] workspace"
- "Apply the [template] template"

#### Channel Management
- "Create a [name] channel"
- "Delete the [name] channel"
- "Organize my channels"
- "Move [channel] to [category]"

#### Role Management
- "Create a [name] role"
- "Delete the [name] role"
- "Set up role hierarchy for [purpose]"
- "Color my roles with [scheme]"

#### Analysis
- "What's missing from this server?"
- "Analyze my server structure"
- "How can I improve organization?"

#### Bulk Operations
- "Delete all channels with [pattern]"
- "Assign [role] to new members"
- "Clean up unused roles"

## 🏗️ Architecture

### Project Structure
```
├── main.py                 # Entry point
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── src/
│   ├── core/
│   │   ├── bot.py         # Main bot class
│   │   └── commands.py    # Core commands (setup, clear, AI)
│   ├── services/
│   │   ├── ai_service.py          # Gemini AI integration
│   │   ├── action_dispatcher.py  # Action execution engine
│   │   ├── server_context.py     # Server analysis
│   │   ├── channel_manager.py    # Advanced channel operations
│   │   ├── role_manager.py       # Advanced role operations
│   │   └── server_templates.py   # Complete server templates
│   └── utils/
│       ├── logger.py      # Comprehensive logging
│       └── persistence.py # Data persistence
└── logs/                  # Log files
```

### Key Components

1. **AI Service**: Processes natural language using Gemini API and converts to structured actions
2. **Action Dispatcher**: Executes structured actions on Discord servers
3. **Server Context Service**: Analyzes current server state for AI processing
4. **Channel/Role Managers**: Handle advanced server management operations
5. **Template Service**: Applies complete server layouts
6. **Persistence Manager**: Maintains bot state across restarts
7. **Logger**: Comprehensive logging system for debugging and auditing

## 🔧 Configuration

### Environment Variables
- `DISCORD_TOKEN`: Your Discord bot token
- `GEMINI_API_KEY`: Your Google Gemini API key

### Bot Settings (config.py)
- `BOT_PREFIX`: Command prefix (default: '!')
- `SETUP_CHANNEL_NAME`: Name for setup channels (default: 'server-setup')
- `LOG_LEVEL`: Logging level (default: 'INFO')

## 📊 Logging

The bot maintains comprehensive logs in the `logs/` directory:

- **Command Processing**: Every user command with full context
- **Action Execution**: Detailed execution logs for all server modifications
- **Error Tracking**: Complete error logs with stack traces
- **Performance Metrics**: Timing and success/failure rates

Example log entry:
```
=== BOT PROCESSING LOG ===
Timestamp: 2024-01-15 14:30:22
Server: MyServer (ID: 123456789)
User: @admin#1234
Input: "create a support channel"
Parsed Intent: create_channel
Generated Actions: 1
Execution Status: SUCCESS
Result: Created #support in General category
=== END LOG ===
```

## 🛡️ Security

- **Administrator-Only**: Setup channels are only visible to administrators
- **Permission Validation**: All actions validate user permissions before execution
- **Audit Trail**: Complete logging of all administrative actions
- **Safe Defaults**: Conservative permission settings for new channels/roles
- **Setup Channel Protection**: Cannot delete setup channels through bot commands

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter issues:

1. Check the logs in the `logs/` directory
2. Ensure your bot has the required permissions
3. Verify your API keys are correct
4. Check the Discord API status

For additional support, please open an issue on GitHub.
