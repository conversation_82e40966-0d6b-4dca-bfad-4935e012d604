"""
Test script for the Discord Server Management Bot.
This script tests various components without requiring a live Discord connection.
"""
import asyncio
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.services.ai_service import AIService
from src.utils.logger import bot_logger
from src.utils.persistence import PersistenceManager

async def test_ai_service():
    """Test the AI service with various inputs."""
    print("🤖 Testing AI Service...")
    
    ai_service = AIService()
    
    test_commands = [
        "Create a support channel",
        "Design a server for my gaming community",
        "Delete all test channels",
        "Set up a professional workspace",
        "What's missing from this server?",
        "Organize my messy server",
        "Create a role hierarchy for gaming",
        "Apply rainbow colors to my roles"
    ]
    
    for command in test_commands:
        try:
            print(f"\n📝 Testing: '{command}'")
            result = await ai_service.parse_intent(command)
            print(f"✅ Actions: {len(result.get('actions', []))}")
            print(f"📋 Summary: {result.get('summary', 'No summary')}")
            
            # Print first action for verification
            if result.get('actions'):
                first_action = result['actions'][0]
                print(f"🔧 First Action: {first_action.get('type', 'unknown')} - {first_action.get('name', 'N/A')}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n✅ AI Service tests completed!")

async def test_persistence():
    """Test the persistence manager."""
    print("\n💾 Testing Persistence Manager...")
    
    persistence = PersistenceManager("test_data.json")
    
    try:
        # Test saving and loading data
        await persistence.add_setup_channel(12345, 67890)
        await persistence.add_setup_channel(11111, 22222)
        
        channels = await persistence.get_setup_channels()
        print(f"✅ Saved channels: {channels}")
        
        # Test server config
        await persistence.set_server_config(12345, {"template": "gaming", "created": "2024-01-01"})
        config = await persistence.get_server_config(12345)
        print(f"✅ Server config: {config}")
        
        # Cleanup
        os.remove("test_data.json")
        print("✅ Persistence tests completed!")
        
    except Exception as e:
        print(f"❌ Persistence error: {e}")

def test_logging():
    """Test the logging system."""
    print("\n📝 Testing Logging System...")
    
    try:
        bot_logger.info("Test info message")
        bot_logger.warning("Test warning message")
        bot_logger.debug("Test debug message")
        
        # Test command logging
        bot_logger.log_command(
            server_id=12345,
            server_name="Test Server",
            user="TestUser#1234",
            command="test command",
            intent="test_intent",
            actions_count=3,
            status="SUCCESS",
            result="Test completed successfully"
        )
        
        print("✅ Logging tests completed!")
        
    except Exception as e:
        print(f"❌ Logging error: {e}")

def test_imports():
    """Test that all modules can be imported successfully."""
    print("\n📦 Testing Module Imports...")
    
    try:
        from src.core.bot import DiscordServerBot
        from src.core.commands import SetupCommands, ClearCommands, AICommands
        from src.services.action_dispatcher import ActionDispatcher
        from src.services.server_context import ServerContextService
        from src.services.channel_manager import ChannelManager
        from src.services.role_manager import RoleManager
        from src.services.server_templates import ServerTemplateService
        import config
        
        print("✅ All modules imported successfully!")
        
    except Exception as e:
        print(f"❌ Import error: {e}")

async def run_tests():
    """Run all tests."""
    print("🚀 Starting Discord Bot Tests...\n")
    
    # Test imports first
    test_imports()
    
    # Test logging
    test_logging()
    
    # Test persistence
    await test_persistence()
    
    # Test AI service (requires API key)
    try:
        await test_ai_service()
    except Exception as e:
        print(f"⚠️ AI Service test skipped (likely missing API key): {e}")
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    asyncio.run(run_tests())
