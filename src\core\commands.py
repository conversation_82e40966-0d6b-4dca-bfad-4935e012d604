"""
Core bot commands including setup functionality.
"""
import discord
from discord.ext import commands
from typing import Optional
import config
from src.utils.logger import bot_logger

class SetupCommands(commands.Cog):
    """Commands for bot setup and configuration."""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name='setup')
    @commands.has_permissions(administrator=True)
    async def setup_command(self, ctx):
        """Initialize the bot for this server by creating a server-setup channel."""
        guild = ctx.guild
        if not guild:
            await ctx.send("❌ This command can only be used in a server.")
            return
        
        # Check if setup channel already exists
        existing_channel_id = self.bot.get_setup_channel_id(guild.id)
        if existing_channel_id:
            existing_channel = guild.get_channel(existing_channel_id)
            if existing_channel:
                await ctx.send(f"✅ Server setup is already complete! Use {existing_channel.mention} for bot commands.")
                return
            else:
                # Channel was deleted, remove from tracking
                await self.bot.remove_setup_channel(guild.id)
        
        try:
            # Create the server-setup channel
            setup_channel = await self.create_setup_channel(guild)
            
            # Add to bot's tracking
            await self.bot.add_setup_channel(guild.id, setup_channel.id)
            
            # Send confirmation
            embed = discord.Embed(
                title="🎉 Bot Setup Complete!",
                description=f"Created {setup_channel.mention} for server management.",
                color=discord.Color.green()
            )
            embed.add_field(
                name="📋 What's Next?",
                value=(
                    f"• Use {setup_channel.mention} to give me natural language commands\n"
                    f"• Try: `Design a server for my gaming community`\n"
                    f"• Try: `Create a support channel`\n"
                    f"• Try: `What's missing from this server?`\n"
                    f"• Use `!clear` to reset the server (removes all channels/roles except this one)"
                ),
                inline=False
            )
            embed.add_field(
                name="🔒 Security",
                value="Only administrators can see and use the setup channel.",
                inline=False
            )
            
            await ctx.send(embed=embed)
            
            # Send welcome message to the setup channel
            welcome_embed = discord.Embed(
                title="🤖 Welcome to Server Management!",
                description="I'm your AI-powered Discord server assistant. I can help you manage your server through natural language commands.",
                color=discord.Color.blue()
            )
            welcome_embed.add_field(
                name="💡 Example Commands",
                value=(
                    "• `Design a server for my gaming community`\n"
                    "• `Create a support channel in a Help category`\n"
                    "• `Delete the general channel`\n"
                    "• `What's missing from this server?`\n"
                    "• `Remove announcements and create updates channel instead`"
                ),
                inline=False
            )
            welcome_embed.add_field(
                name="🔧 Special Commands",
                value="• `!clear` - Reset server (removes all channels/categories/roles except admin roles)",
                inline=False
            )
            
            await setup_channel.send(embed=welcome_embed)
            
            bot_logger.log_command(
                server_id=guild.id,
                server_name=guild.name,
                user=str(ctx.author),
                command="!setup",
                intent="setup_bot",
                actions_count=1,
                status="SUCCESS",
                result=f"Created setup channel #{setup_channel.name}"
            )
            
        except discord.Forbidden:
            await ctx.send("❌ I don't have permission to create channels. Please ensure I have the 'Manage Channels' permission.")
        except Exception as e:
            bot_logger.error(f"Error during setup in {guild.name}: {e}", exc_info=True)
            await ctx.send("❌ An error occurred during setup. Please try again.")
    
    async def create_setup_channel(self, guild: discord.Guild) -> discord.TextChannel:
        """Create the server-setup channel with proper permissions."""
        # Define permissions for the setup channel
        overwrites = {
            guild.default_role: discord.PermissionOverwrite(view_channel=False),  # Hide from @everyone
            guild.me: discord.PermissionOverwrite(
                view_channel=True,
                send_messages=True,
                read_message_history=True,
                manage_messages=True
            )
        }
        
        # Add permissions for administrators
        for role in guild.roles:
            if role.permissions.administrator:
                overwrites[role] = discord.PermissionOverwrite(
                    view_channel=True,
                    send_messages=True,
                    read_message_history=True,
                    use_slash_commands=True
                )
        
        # Create the channel
        channel = await guild.create_text_channel(
            name=config.SETUP_CHANNEL_NAME,
            overwrites=overwrites,
            topic="🤖 AI-powered server management. Only administrators can see this channel.",
            reason="Bot setup - creating management channel"
        )
        
        bot_logger.info(f"Created setup channel #{channel.name} in {guild.name}")
        return channel
    
    @setup_command.error
    async def setup_error(self, ctx, error):
        """Handle setup command errors."""
        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You need Administrator permissions to set up the bot.")
        else:
            bot_logger.error(f"Setup command error: {error}", exc_info=True)
            await ctx.send("❌ An error occurred during setup. Please try again.")

class ClearCommands(commands.Cog):
    """Commands for server reset functionality."""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name='clear')
    async def clear_command(self, ctx):
        """Reset the server by removing all channels and roles except setup channel and admin roles."""
        # Only work in setup channels
        if not self.bot.is_setup_channel(ctx.channel.id):
            return
        
        # Check permissions
        if not ctx.author.guild_permissions.administrator:
            await ctx.send("❌ You need Administrator permissions to use this command.")
            return
        
        guild = ctx.guild
        setup_channel_id = self.bot.get_setup_channel_id(guild.id)
        
        # Confirmation message
        embed = discord.Embed(
            title="⚠️ Server Reset Confirmation",
            description="This will delete ALL channels, categories, and roles except:\n• This setup channel\n• Administrator roles\n\n**This action cannot be undone!**",
            color=discord.Color.red()
        )
        embed.add_field(
            name="React to confirm:",
            value="✅ Yes, reset the server\n❌ No, cancel",
            inline=False
        )
        
        message = await ctx.send(embed=embed)
        await message.add_reaction("✅")
        await message.add_reaction("❌")
        
        def check(reaction, user):
            return (user == ctx.author and 
                   str(reaction.emoji) in ["✅", "❌"] and 
                   reaction.message.id == message.id)
        
        try:
            reaction, user = await self.bot.wait_for('reaction_add', timeout=30.0, check=check)
            
            if str(reaction.emoji) == "❌":
                await message.edit(embed=discord.Embed(
                    title="❌ Reset Cancelled",
                    description="Server reset has been cancelled.",
                    color=discord.Color.blue()
                ))
                return
            
            # Proceed with reset
            await message.edit(embed=discord.Embed(
                title="🔄 Resetting Server...",
                description="Please wait while I reset the server. This may take a moment.",
                color=discord.Color.orange()
            ))
            
            deleted_channels = 0
            deleted_categories = 0
            deleted_roles = 0
            
            # Delete all channels except setup channel
            for channel in guild.channels:
                if channel.id != setup_channel_id:
                    try:
                        if isinstance(channel, discord.CategoryChannel):
                            await channel.delete(reason="Server reset by administrator")
                            deleted_categories += 1
                        else:
                            await channel.delete(reason="Server reset by administrator")
                            deleted_channels += 1
                    except Exception as e:
                        bot_logger.warning(f"Could not delete channel {channel.name}: {e}")
            
            # Delete all roles except admin roles and @everyone
            for role in guild.roles:
                if (role != guild.default_role and 
                    not role.permissions.administrator and 
                    role < guild.me.top_role):
                    try:
                        await role.delete(reason="Server reset by administrator")
                        deleted_roles += 1
                    except Exception as e:
                        bot_logger.warning(f"Could not delete role {role.name}: {e}")
            
            # Send completion message
            embed = discord.Embed(
                title="✅ Server Reset Complete",
                description=f"Successfully reset the server!",
                color=discord.Color.green()
            )
            embed.add_field(
                name="📊 Summary",
                value=f"• Deleted {deleted_channels} channels\n• Deleted {deleted_categories} categories\n• Deleted {deleted_roles} roles",
                inline=False
            )
            embed.add_field(
                name="🎯 Next Steps",
                value="You can now use natural language commands to rebuild your server structure.",
                inline=False
            )
            
            await message.edit(embed=embed)
            
            bot_logger.log_command(
                server_id=guild.id,
                server_name=guild.name,
                user=str(ctx.author),
                command="!clear",
                intent="reset_server",
                actions_count=deleted_channels + deleted_categories + deleted_roles,
                status="SUCCESS",
                result=f"Deleted {deleted_channels} channels, {deleted_categories} categories, {deleted_roles} roles"
            )
            
        except Exception as e:
            bot_logger.error(f"Error during server reset: {e}", exc_info=True)
            await message.edit(embed=discord.Embed(
                title="❌ Reset Failed",
                description="An error occurred during the reset. Some items may not have been deleted.",
                color=discord.Color.red()
            ))

class AICommands(commands.Cog):
    """AI-powered natural language command processing."""

    def __init__(self, bot):
        self.bot = bot
        # Import here to avoid circular imports
        from src.services.ai_service import ai_service
        from src.services.action_dispatcher import ActionDispatcher
        from src.services.server_context import ServerContextService

        self.ai_service = ai_service
        self.action_dispatcher = ActionDispatcher(bot)
        self.context_service = ServerContextService()

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle natural language commands in setup channels."""
        # Skip if bot message or not in setup channel
        if message.author.bot or not self.bot.is_setup_channel(message.channel.id):
            return

        # Skip if it's a bot command (starts with prefix)
        if message.content.startswith(self.bot.command_prefix):
            return

        # Check if user has admin permissions
        if not message.author.guild_permissions.administrator:
            await message.channel.send("❌ You need Administrator permissions to use AI commands.")
            return

        guild = message.guild
        user_input = message.content.strip()

        if not user_input:
            return

        # Show typing indicator
        async with message.channel.typing():
            try:
                # Get server context
                server_context = await self.context_service.get_server_context(guild)

                # Parse intent with AI
                bot_logger.info(f"Processing AI command from {message.author} in {guild.name}: {user_input}")
                parsed_result = await self.ai_service.parse_intent(user_input, server_context)

                actions = parsed_result.get('actions', [])
                summary = parsed_result.get('summary', 'Processing your request...')

                # Log the command
                bot_logger.log_command(
                    server_id=guild.id,
                    server_name=guild.name,
                    user=str(message.author),
                    command=user_input,
                    intent="ai_command",
                    actions_count=len(actions),
                    status="PROCESSING"
                )

                if not actions:
                    await message.channel.send(f"🤔 {summary}")
                    return

                # Create response embed
                embed = discord.Embed(
                    title="🔧 Planned Actions",
                    description=summary,
                    color=discord.Color.blue()
                )

                # Add action details
                if len(actions) <= 5:  # Show details for small action lists
                    action_details = []
                    for action in actions:
                        action_type = action.get('type', 'unknown')
                        name = action.get('name', 'N/A')
                        if action_type == 'analyze_server':
                            action_details.append("📊 Analyzing server structure")
                        elif action_type == 'create_channel':
                            channel_type = action.get('channel_type', 'text')
                            category = action.get('category', 'No category')
                            action_details.append(f"📝 Create {channel_type} channel #{name} in {category}")
                        elif action_type == 'create_category':
                            action_details.append(f"📁 Create category '{name}'")
                        elif action_type == 'delete_channel':
                            action_details.append(f"🗑️ Delete channel #{name}")
                        elif action_type == 'delete_category':
                            action_details.append(f"🗑️ Delete category '{name}'")
                        elif action_type == 'create_role':
                            action_details.append(f"👥 Create role '{name}'")
                        elif action_type == 'delete_role':
                            action_details.append(f"🗑️ Delete role '{name}'")
                        else:
                            action_details.append(f"⚙️ {action_type}: {name}")

                    if action_details:
                        embed.add_field(
                            name="📋 Actions",
                            value="\n".join(action_details),
                            inline=False
                        )
                else:
                    embed.add_field(
                        name="📋 Actions",
                        value=f"Will execute {len(actions)} actions",
                        inline=False
                    )

                embed.add_field(
                    name="⚡ Status",
                    value="Executing now...",
                    inline=False
                )

                # Send initial response
                response_message = await message.channel.send(embed=embed)

                # Execute actions
                success_messages, error_messages = await self.action_dispatcher.execute_actions(actions, guild)

                # Update response with results
                if success_messages or error_messages:
                    result_embed = discord.Embed(
                        title="✅ Execution Complete" if not error_messages else "⚠️ Execution Complete with Errors",
                        description=summary,
                        color=discord.Color.green() if not error_messages else discord.Color.orange()
                    )

                    if success_messages:
                        # Handle long success messages
                        success_text = "\n".join(f"✅ {msg}" for msg in success_messages)
                        if len(success_text) > 1024:
                            success_text = f"✅ Successfully completed {len(success_messages)} actions"

                        result_embed.add_field(
                            name="✅ Successful Actions",
                            value=success_text,
                            inline=False
                        )

                    if error_messages:
                        error_text = "\n".join(f"❌ {msg}" for msg in error_messages)
                        if len(error_text) > 1024:
                            error_text = f"❌ {len(error_messages)} actions failed"

                        result_embed.add_field(
                            name="❌ Failed Actions",
                            value=error_text,
                            inline=False
                        )

                    await response_message.edit(embed=result_embed)

                    # Log final result
                    final_status = "SUCCESS" if not error_messages else "PARTIAL_SUCCESS" if success_messages else "FAILED"
                    result_summary = f"Success: {len(success_messages)}, Errors: {len(error_messages)}"

                    bot_logger.log_command(
                        server_id=guild.id,
                        server_name=guild.name,
                        user=str(message.author),
                        command=user_input,
                        intent="ai_command",
                        actions_count=len(actions),
                        status=final_status,
                        result=result_summary
                    )
                else:
                    # No actions were executed
                    await response_message.edit(embed=discord.Embed(
                        title="ℹ️ No Actions Executed",
                        description=summary,
                        color=discord.Color.blue()
                    ))

            except Exception as e:
                bot_logger.error(f"Error processing AI command: {e}", exc_info=True)

                error_embed = discord.Embed(
                    title="❌ Error",
                    description="An error occurred while processing your command. Please try again.",
                    color=discord.Color.red()
                )

                try:
                    await message.channel.send(embed=error_embed)
                except:
                    pass  # Channel might be deleted or bot lacks permissions

async def setup(bot):
    """Load the cogs."""
    await bot.add_cog(SetupCommands(bot))
    await bot.add_cog(ClearCommands(bot))
    await bot.add_cog(AICommands(bot))
