"""
Core bot commands including setup functionality.
"""
import discord
from discord.ext import commands
from typing import Optional
import config
from src.utils.logger import bot_logger

class SetupCommands(commands.Cog):
    """Commands for bot setup and configuration."""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name='setup')
    @commands.has_permissions(administrator=True)
    async def setup_command(self, ctx):
        """Initialize the bot for this server by creating a server-setup channel."""
        guild = ctx.guild
        if not guild:
            await ctx.send("❌ This command can only be used in a server.")
            return
        
        # Check if setup channel already exists
        existing_channel_id = self.bot.get_setup_channel_id(guild.id)
        if existing_channel_id:
            existing_channel = guild.get_channel(existing_channel_id)
            if existing_channel:
                await ctx.send(f"✅ Server setup is already complete! Use {existing_channel.mention} for bot commands.")
                return
            else:
                # Channel was deleted, remove from tracking
                await self.bot.remove_setup_channel(guild.id)
        
        try:
            # Create the server-setup channel
            setup_channel = await self.create_setup_channel(guild)
            
            # Add to bot's tracking
            await self.bot.add_setup_channel(guild.id, setup_channel.id)
            
            # Send confirmation
            embed = discord.Embed(
                title="🎉 Bot Setup Complete!",
                description=f"Created {setup_channel.mention} for server management.",
                color=discord.Color.green()
            )
            embed.add_field(
                name="📋 What's Next?",
                value=(
                    f"• Use {setup_channel.mention} to give me natural language commands\n"
                    f"• Try: `Design a server for my gaming community`\n"
                    f"• Try: `Create a support channel`\n"
                    f"• Try: `What's missing from this server?`\n"
                    f"• Use `!clear` to reset the server (removes all channels/roles except this one)"
                ),
                inline=False
            )
            embed.add_field(
                name="🔒 Security",
                value="Only administrators can see and use the setup channel.",
                inline=False
            )
            
            await ctx.send(embed=embed)
            
            # Send welcome message to the setup channel
            welcome_embed = discord.Embed(
                title="🤖 Welcome to Server Management!",
                description="I'm your AI-powered Discord server assistant. I can help you manage your server through natural language commands.",
                color=discord.Color.blue()
            )
            welcome_embed.add_field(
                name="💡 Example Commands",
                value=(
                    "• `Design a server for my gaming community`\n"
                    "• `Create a support channel in a Help category`\n"
                    "• `Delete the general channel`\n"
                    "• `What's missing from this server?`\n"
                    "• `Remove announcements and create updates channel instead`"
                ),
                inline=False
            )
            welcome_embed.add_field(
                name="🔧 Special Commands",
                value="• `!clear` - Reset server (removes all channels/categories/roles except admin roles)",
                inline=False
            )
            
            await setup_channel.send(embed=welcome_embed)
            
            bot_logger.log_command(
                server_id=guild.id,
                server_name=guild.name,
                user=str(ctx.author),
                command="!setup",
                intent="setup_bot",
                actions_count=1,
                status="SUCCESS",
                result=f"Created setup channel #{setup_channel.name}"
            )
            
        except discord.Forbidden:
            await ctx.send("❌ I don't have permission to create channels. Please ensure I have the 'Manage Channels' permission.")
        except Exception as e:
            bot_logger.error(f"Error during setup in {guild.name}: {e}", exc_info=True)
            await ctx.send("❌ An error occurred during setup. Please try again.")
    
    async def create_setup_channel(self, guild: discord.Guild) -> discord.TextChannel:
        """Create the server-setup channel with proper permissions."""
        # Define permissions for the setup channel
        overwrites = {
            guild.default_role: discord.PermissionOverwrite(view_channel=False),  # Hide from @everyone
            guild.me: discord.PermissionOverwrite(
                view_channel=True,
                send_messages=True,
                read_message_history=True,
                manage_messages=True
            )
        }
        
        # Add permissions for administrators
        for role in guild.roles:
            if role.permissions.administrator:
                overwrites[role] = discord.PermissionOverwrite(
                    view_channel=True,
                    send_messages=True,
                    read_message_history=True
                )
        
        # Create the channel
        channel = await guild.create_text_channel(
            name=config.SETUP_CHANNEL_NAME,
            overwrites=overwrites,
            topic="🤖 AI-powered server management. Only administrators can see this channel.",
            reason="Bot setup - creating management channel"
        )
        
        bot_logger.info(f"Created setup channel #{channel.name} in {guild.name}")
        return channel
    
    @setup_command.error
    async def setup_error(self, ctx, error):
        """Handle setup command errors."""
        if isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You need Administrator permissions to set up the bot.")
        else:
            bot_logger.error(f"Setup command error: {error}", exc_info=True)
            await ctx.send("❌ An error occurred during setup. Please try again.")

class ClearCommands(commands.Cog):
    """Commands for server reset functionality."""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name='clear')
    async def clear_command(self, ctx):
        """Reset the server by removing all channels and roles except setup channel and admin roles."""
        # Only work in setup channels
        if not self.bot.is_setup_channel(ctx.channel.id):
            return
        
        # Check permissions
        if not ctx.author.guild_permissions.administrator:
            await ctx.send("❌ You need Administrator permissions to use this command.")
            return
        
        guild = ctx.guild
        setup_channel_id = self.bot.get_setup_channel_id(guild.id)
        
        # Confirmation message
        embed = discord.Embed(
            title="⚠️ Server Reset Confirmation",
            description="This will delete ALL channels, categories, and roles except:\n• This setup channel\n• Administrator roles\n\n**This action cannot be undone!**",
            color=discord.Color.red()
        )
        embed.add_field(
            name="React to confirm:",
            value="✅ Yes, reset the server\n❌ No, cancel",
            inline=False
        )
        
        message = await ctx.send(embed=embed)
        await message.add_reaction("✅")
        await message.add_reaction("❌")
        
        def check(reaction, user):
            return (user == ctx.author and 
                   str(reaction.emoji) in ["✅", "❌"] and 
                   reaction.message.id == message.id)
        
        try:
            reaction, user = await self.bot.wait_for('reaction_add', timeout=30.0, check=check)
            
            if str(reaction.emoji) == "❌":
                await message.edit(embed=discord.Embed(
                    title="❌ Reset Cancelled",
                    description="Server reset has been cancelled.",
                    color=discord.Color.blue()
                ))
                return
            
            # Proceed with reset
            await message.edit(embed=discord.Embed(
                title="🔄 Resetting Server...",
                description="Please wait while I reset the server. This may take a moment.",
                color=discord.Color.orange()
            ))
            
            deleted_channels = 0
            deleted_categories = 0
            deleted_roles = 0
            
            # Delete all channels except setup channel
            for channel in guild.channels:
                if channel.id != setup_channel_id:
                    try:
                        if isinstance(channel, discord.CategoryChannel):
                            await channel.delete(reason="Server reset by administrator")
                            deleted_categories += 1
                        else:
                            await channel.delete(reason="Server reset by administrator")
                            deleted_channels += 1
                    except Exception as e:
                        bot_logger.warning(f"Could not delete channel {channel.name}: {e}")
            
            # Delete all roles except admin roles and @everyone
            for role in guild.roles:
                if (role != guild.default_role and 
                    not role.permissions.administrator and 
                    role < guild.me.top_role):
                    try:
                        await role.delete(reason="Server reset by administrator")
                        deleted_roles += 1
                    except Exception as e:
                        bot_logger.warning(f"Could not delete role {role.name}: {e}")
            
            # Send completion message
            embed = discord.Embed(
                title="✅ Server Reset Complete",
                description=f"Successfully reset the server!",
                color=discord.Color.green()
            )
            embed.add_field(
                name="📊 Summary",
                value=f"• Deleted {deleted_channels} channels\n• Deleted {deleted_categories} categories\n• Deleted {deleted_roles} roles",
                inline=False
            )
            embed.add_field(
                name="🎯 Next Steps",
                value="You can now use natural language commands to rebuild your server structure.",
                inline=False
            )
            
            await message.edit(embed=embed)
            
            bot_logger.log_command(
                server_id=guild.id,
                server_name=guild.name,
                user=str(ctx.author),
                command="!clear",
                intent="reset_server",
                actions_count=deleted_channels + deleted_categories + deleted_roles,
                status="SUCCESS",
                result=f"Deleted {deleted_channels} channels, {deleted_categories} categories, {deleted_roles} roles"
            )
            
        except Exception as e:
            bot_logger.error(f"Error during server reset: {e}", exc_info=True)
            await message.edit(embed=discord.Embed(
                title="❌ Reset Failed",
                description="An error occurred during the reset. Some items may not have been deleted.",
                color=discord.Color.red()
            ))

class AICommands(commands.Cog):
    """AI-powered natural language command processing."""

    def __init__(self, bot):
        self.bot = bot
        # Import here to avoid circular imports
        from src.services.ai_service import ai_service
        from src.services.action_dispatcher import ActionDispatcher
        from src.services.server_context import ServerContextService
        from src.services.response_formatter import ResponseFormatter
        from src.services.context_memory import ContextualMemorySystem

        self.ai_service = ai_service
        self.action_dispatcher = ActionDispatcher(bot)
        self.context_service = ServerContextService()
        self.response_formatter = ResponseFormatter()
        self.memory_system = ContextualMemorySystem()

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle natural language commands in setup channels."""
        # Skip if bot message or not in setup channel
        if message.author.bot or not self.bot.is_setup_channel(message.channel.id):
            return

        # Skip if it's a bot command (starts with prefix)
        if message.content.startswith(self.bot.command_prefix):
            return

        # Check if user has admin permissions
        if not message.author.guild_permissions.administrator:
            await message.channel.send("❌ You need Administrator permissions to use AI commands.")
            return

        guild = message.guild
        user_input = message.content.strip()

        if not user_input:
            return

        # Show typing indicator
        async with message.channel.typing():
            try:
                start_time = time.time()

                # Check for conversation context and follow-up commands
                conversation_context = self.memory_system.get_conversation_context(guild.id, message.author.id)
                context_hints = {}

                if conversation_context:
                    follow_up_analysis = self.memory_system.detect_follow_up_context(user_input, conversation_context)
                    if follow_up_analysis["is_follow_up"]:
                        context_hints = follow_up_analysis["context_hints"]
                        bot_logger.info(f"Detected follow-up command with context: {context_hints}")

                # Get server context
                server_context = await self.context_service.get_server_context(guild)

                # Add context hints to server context
                if context_hints:
                    server_context["conversation_context"] = context_hints

                # Parse intent with enhanced AI (multi-step detection)
                bot_logger.info(f"Processing enhanced AI command from {message.author} in {guild.name}: {user_input}")
                parsing_start = time.time()
                parsed_result = await self.ai_service.parse_intent(user_input, server_context)
                parsing_time = time.time() - parsing_start

                # Log AI parsing performance
                intents = parsed_result.get('intents', [])
                actions = parsed_result.get('actions', [])
                bot_logger.log_ai_parsing(
                    user_request=user_input,
                    parsing_time=parsing_time,
                    intents_detected=len(intents),
                    actions_generated=len(actions),
                    success=bool(actions)
                )

                if not actions:
                    error_response = self.response_formatter.format_error_response(
                        parsed_result.get('summary', 'I could not understand your request.'),
                        ["Try rephrasing with more specific details", "Break complex requests into smaller steps"]
                    )
                    await message.channel.send(error_response)
                    return

                # Check if this is a multi-step operation
                execution_plan = parsed_result.get('execution_plan', {})
                is_multi_step = len(actions) > 1 or execution_plan.get('risk_level') in ['medium', 'high']

                # Create initial response
                if is_multi_step:
                    # Show execution plan for complex operations
                    plan_embed = discord.Embed(
                        title="🧠 **Multi-Step Operation Detected**",
                        description=parsed_result.get('summary', 'Processing complex request...'),
                        color=discord.Color.orange()
                    )

                    # Show intents
                    if intents:
                        intent_list = []
                        for intent in intents[:5]:  # Limit to 5 intents
                            priority_emoji = "🥇" if intent.get('priority', 1) == 1 else "🥈" if intent.get('priority', 1) == 2 else "🥉"
                            intent_list.append(f"{priority_emoji} {intent.get('description', 'Unknown intent')}")

                        plan_embed.add_field(
                            name="🎯 **Detected Intents**",
                            value="\n".join(intent_list),
                            inline=False
                        )

                    # Show execution plan
                    if execution_plan:
                        plan_details = []
                        plan_details.append(f"**Steps:** {execution_plan.get('total_steps', len(actions))}")
                        plan_details.append(f"**Estimated Time:** {execution_plan.get('estimated_time', 'Unknown')}")
                        plan_details.append(f"**Risk Level:** {execution_plan.get('risk_level', 'Low').title()}")

                        plan_embed.add_field(
                            name="📋 **Execution Plan**",
                            value="\n".join(plan_details),
                            inline=False
                        )

                    plan_embed.add_field(
                        name="⚡ **Starting Execution**",
                        value="Please wait while I execute these actions...",
                        inline=False
                    )

                    status_message = await message.channel.send(embed=plan_embed)
                else:
                    # Simple operation - show basic plan
                    simple_embed = discord.Embed(
                        title="🔧 Processing Request",
                        description=parsed_result.get('summary', 'Processing your request...'),
                        color=discord.Color.blue()
                    )
                    status_message = await message.channel.send(embed=simple_embed)

                # Define progress callback for multi-step operations
                async def progress_callback(current_step: int, total_steps: int, action_description: str):
                    if is_multi_step and current_step % 2 == 0:  # Update every 2 steps to avoid spam
                        progress_text = self.response_formatter.format_progress_update(
                            current_step, total_steps, action_description
                        )
                        try:
                            await status_message.edit(content=progress_text, embed=None)
                        except discord.NotFound:
                            pass  # Message was deleted

                # Execute actions using enhanced execution engine
                if is_multi_step:
                    success_messages, error_messages, execution_summary = await self.action_dispatcher.execute_enhanced_actions(
                        parsed_result, guild, progress_callback
                    )
                else:
                    # Use simple execution for single actions
                    success_messages, error_messages = await self.action_dispatcher.execute_actions(actions, guild)
                    execution_summary = {
                        'total_actions': len(actions),
                        'completed_actions': len(success_messages),
                        'failed_actions': len(error_messages),
                        'execution_time': time.time() - start_time,
                        'rollback_performed': False
                    }

                # Format comprehensive response using enhanced response system
                total_execution_time = time.time() - start_time
                execution_results = (success_messages, error_messages, execution_summary)

                # Use enhanced response formatter for structured feedback
                formatted_response = self.response_formatter.format_multi_step_response(
                    user_request=user_input,
                    parsed_response=parsed_result,
                    execution_results=execution_results,
                    execution_time=total_execution_time
                )

                # Send formatted response (split if too long for Discord)
                if len(formatted_response) > 2000:
                    # Split into multiple messages
                    parts = []
                    current_part = ""
                    for line in formatted_response.split('\n'):
                        if len(current_part + line + '\n') > 1900:
                            parts.append(current_part)
                            current_part = line + '\n'
                        else:
                            current_part += line + '\n'
                    if current_part:
                        parts.append(current_part)

                    # Edit first message and send additional parts
                    await status_message.edit(content=parts[0], embed=None)
                    for part in parts[1:]:
                        await message.channel.send(part)
                else:
                    await status_message.edit(content=formatted_response, embed=None)

                # Comprehensive logging for multi-step operations
                execution_sequence = []
                for i, action in enumerate(actions):
                    action_id = action.get('action_id', f'action_{i+1}')
                    action_type = action.get('type', 'unknown')

                    # Determine status based on results
                    if i < len(success_messages):
                        status = "completed"
                        result = success_messages[i] if i < len(success_messages) else ""
                        error = ""
                    elif i < len(error_messages):
                        status = "failed"
                        result = ""
                        error = error_messages[i] if i < len(error_messages) else ""
                    else:
                        status = "unknown"
                        result = ""
                        error = ""

                    execution_sequence.append({
                        'action_id': action_id,
                        'action_type': action_type,
                        'status': status,
                        'result': result,
                        'error': error
                    })

                # Log comprehensive multi-step operation
                intent_descriptions = [intent.get('description', '') for intent in intents]
                bot_logger.log_multi_step_operation(
                    user_id=message.author.id,
                    guild_id=guild.id,
                    guild_name=guild.name,
                    user_request=user_input,
                    parsed_intents=intent_descriptions,
                    execution_sequence=execution_sequence,
                    execution_summary=execution_summary
                )

                # Store conversation context for follow-up commands
                self.memory_system.add_command_to_history(
                    guild_id=guild.id,
                    user_id=message.author.id,
                    user_request=user_input,
                    parsed_response=parsed_result,
                    execution_results=execution_results
                )

            except Exception as e:
                bot_logger.error(f"Error processing enhanced AI command: {e}", exc_info=True)

                # Use enhanced error formatting
                error_response = self.response_formatter.format_error_response(
                    f"An error occurred while processing your command: {str(e)}",
                    [
                        "Try rephrasing your request with simpler language",
                        "Break complex requests into smaller steps",
                        "Check that I have the necessary permissions",
                        "Verify that mentioned channels/roles exist"
                    ]
                )

                try:
                    await message.channel.send(error_response)
                except:
                    pass  # Channel might be deleted or bot lacks permissions

async def setup(bot):
    """Load the cogs."""
    await bot.add_cog(SetupCommands(bot))
    await bot.add_cog(ClearCommands(bot))
    await bot.add_cog(AICommands(bot))
