2025-07-11 18:47:04,916 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:47:04,924 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:47:08,567 - discord_bot - INFO - info:69 - Bo<PERSON> is starting up...
2025-07-11 18:47:08,568 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:47:08,568 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:47:08,569 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:47:11,168 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:47:11,168 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:50:21,830 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:50:24,764 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:50:25,556 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 18:50:25,557 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:50:25,557 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:50:25,558 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:50:28,063 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:50:28,063 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:54:01,610 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:54:03,209 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:54:04,383 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 18:54:04,383 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:54:04,384 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:54:04,384 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:54:07,290 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:54:07,291 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:56:44,042 - discord_bot - ERROR - error:77 - Error during setup in test: no permission called use_slash_commands.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 38, in setup_command
    setup_channel = await self.create_setup_channel(guild)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 126, in create_setup_channel
    overwrites[role] = discord.PermissionOverwrite(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        view_channel=True,
        ^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        use_slash_commands=True
        ^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\permissions.py", line 916, in __init__
    raise ValueError(f'no permission called {key}.')
ValueError: no permission called use_slash_commands.
2025-07-11 18:56:57,455 - discord_bot - ERROR - error:77 - Error during setup in test: no permission called use_slash_commands.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 38, in setup_command
    setup_channel = await self.create_setup_channel(guild)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 126, in create_setup_channel
    overwrites[role] = discord.PermissionOverwrite(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        view_channel=True,
        ^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        use_slash_commands=True
        ^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\permissions.py", line 916, in __init__
    raise ValueError(f'no permission called {key}.')
ValueError: no permission called use_slash_commands.
2025-07-11 18:58:12,701 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:58:14,183 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:58:14,819 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 18:58:14,820 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:58:14,820 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:58:14,821 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:58:17,325 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:58:17,325 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:59:15,054 - discord_bot - INFO - info:69 - Created setup channel #server-setup in test
2025-07-11 18:59:15,054 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:59:15,058 - discord_bot - INFO - info:69 - Added setup channel mapping: Server 1391798815304060981 -> Channel 1393260059521449985
2025-07-11 18:59:15,687 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 18:59:15
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!setup"
Parsed Intent: setup_bot
Generated Actions: 1
Execution Status: SUCCESS
Result: Created setup channel #server-setup
=== END LOG ===
2025-07-11 18:59:26,148 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: Design a server for my gaming community
2025-07-11 18:59:26,494 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 18:59:26,513 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 164, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 18:59:26,537 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 18:59:26
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "Design a server for my gaming community"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:00:04,352 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: Create a support channel in a Help category
2025-07-11 19:00:04,413 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:04,425 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 164, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:04,437 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:00:04
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "Create a support channel in a Help category"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:00:12,023 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: What's missing from this server?
2025-07-11 19:00:12,086 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:12,099 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 164, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:12,111 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:00:12
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "What's missing from this server?"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:01:34,547 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 19:01:36,120 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 19:01:36,755 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 19:01:36,756 - discord_bot - INFO - info:69 - Loaded bot data from bot_data.json
2025-07-11 19:01:36,757 - discord_bot - INFO - info:69 - Loaded 1 setup channels from persistence
2025-07-11 19:01:36,757 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 19:01:39,384 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 19:01:39,385 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 19:02:10,462 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:02:10
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!clear"
Parsed Intent: reset_server
Generated Actions: 1
Execution Status: SUCCESS
Result: Deleted 1 channels, 0 categories, 0 roles
=== END LOG ===
2025-07-11 19:02:38,902 - discord_bot - INFO - info:69 - Test info message
2025-07-11 19:02:38,902 - discord_bot - WARNING - warning:73 - Test warning message
2025-07-11 19:02:38,903 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:02:38
Server: Test Server (ID: 12345)
User: TestUser#1234
Input: "test command"
Parsed Intent: test_intent
Generated Actions: 3
Execution Status: SUCCESS
Result: Test completed successfully
=== END LOG ===
2025-07-11 19:02:38,906 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 19:02:38,912 - discord_bot - INFO - info:69 - Added setup channel mapping: Server 12345 -> Channel 67890
2025-07-11 19:02:38,930 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:38,933 - discord_bot - INFO - info:69 - Added setup channel mapping: Server 11111 -> Channel 22222
2025-07-11 19:02:38,951 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:38,953 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:38,975 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:39,275 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,286 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,364 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,375 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,450 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,460 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,532 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,550 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,620 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,629 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,711 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,719 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,792 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,799 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,867 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,877 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:12:39,036 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 19:12:40,119 - discord_bot - INFO - info:69 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 19:12:40,120 - discord_bot - INFO - info:69 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 19:12:40,126 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 19:12:42,732 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 19:12:42,734 - discord_bot - INFO - info:69 - Loaded bot data from bot_data.json
2025-07-11 19:12:42,744 - discord_bot - INFO - info:69 - Loaded 1 setup channels from persistence
2025-07-11 19:12:42,745 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 19:12:45,233 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 19:12:45,234 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 19:13:44,491 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 19:13:45,509 - discord_bot - INFO - info:69 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 19:13:45,509 - discord_bot - INFO - info:69 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 19:13:45,514 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 19:13:46,150 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 19:13:46,152 - discord_bot - INFO - info:69 - Loaded bot data from bot_data.json
2025-07-11 19:13:46,152 - discord_bot - INFO - info:69 - Loaded 1 setup channels from persistence
2025-07-11 19:13:46,153 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 19:13:48,674 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 19:13:48,675 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 19:15:01,644 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: hello
2025-07-11 19:15:02,224 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:15:02,225 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:02
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "hello"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:15:02,780 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:02
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "hello"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:15:18,698 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a general channel
2025-07-11 19:15:19,011 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:15:19,011 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:19
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a general channel"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:15:19,971 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:19
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a general channel"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:15:33,091 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a voice channel called voice 1
2025-07-11 19:15:33,411 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:15:33,412 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:33
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a voice channel called voice 1"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:15:34,349 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:34
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a voice channel called voice 1"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:15:43,187 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: add emojis to the existing channels
2025-07-11 19:15:43,477 - discord_bot - INFO - info:69 - Successfully parsed intent: 0 actions generated
2025-07-11 19:15:43,478 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:43
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "add emojis to the existing channels"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:16:07,731 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:16:07
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!clear"
Parsed Intent: reset_server
Generated Actions: 2
Execution Status: SUCCESS
Result: Deleted 2 channels, 0 categories, 0 roles
=== END LOG ===
2025-07-11 19:16:29,470 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-11 19:16:29,841 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:16:29,842 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:16:29
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "design a discord server for a minecraft smp"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:16:40,164 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:16:40
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "design a discord server for a minecraft smp"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:17:13,213 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: enhance all channels with emojis before their name
2025-07-11 19:17:14,572 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 19:17:14,573 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:17:14
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "enhance all channels with emojis before their name"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:18:04,073 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a support category
2025-07-11 19:18:04,527 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:18:04,527 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:04
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:18:07,316 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:07
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:18:23,581 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a channel called tickets in the support category
2025-07-11 19:18:24,085 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:18:24,086 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:24
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a channel called tickets in the support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:18:25,246 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:25
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a channel called tickets in the support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:18:48,726 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: what is my server missing?
2025-07-11 19:18:56,235 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 19:18:56,235 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:56
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "what is my server missing?"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:22:27,063 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: adjust the permissions of members so they cant write in <#1393264418955202570>
2025-07-11 19:22:27,622 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 19:22:27,623 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:22:27
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "adjust the permissions of members so they cant write in <#1393264418955202570>"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
