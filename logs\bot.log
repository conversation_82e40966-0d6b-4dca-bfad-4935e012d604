2025-07-11 18:47:04,916 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:47:04,924 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:47:08,567 - discord_bot - INFO - info:69 - Bo<PERSON> is starting up...
2025-07-11 18:47:08,568 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:47:08,568 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:47:08,569 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:47:11,168 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:47:11,168 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:50:21,830 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:50:24,764 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:50:25,556 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 18:50:25,557 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:50:25,557 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:50:25,558 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:50:28,063 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:50:28,063 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:54:01,610 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:54:03,209 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:54:04,383 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 18:54:04,383 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:54:04,384 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:54:04,384 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:54:07,290 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:54:07,291 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:56:44,042 - discord_bot - ERROR - error:77 - Error during setup in test: no permission called use_slash_commands.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 38, in setup_command
    setup_channel = await self.create_setup_channel(guild)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 126, in create_setup_channel
    overwrites[role] = discord.PermissionOverwrite(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        view_channel=True,
        ^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        use_slash_commands=True
        ^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\permissions.py", line 916, in __init__
    raise ValueError(f'no permission called {key}.')
ValueError: no permission called use_slash_commands.
2025-07-11 18:56:57,455 - discord_bot - ERROR - error:77 - Error during setup in test: no permission called use_slash_commands.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 38, in setup_command
    setup_channel = await self.create_setup_channel(guild)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 126, in create_setup_channel
    overwrites[role] = discord.PermissionOverwrite(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        view_channel=True,
        ^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        use_slash_commands=True
        ^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\permissions.py", line 916, in __init__
    raise ValueError(f'no permission called {key}.')
ValueError: no permission called use_slash_commands.
2025-07-11 18:58:12,701 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 18:58:14,183 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 18:58:14,819 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 18:58:14,820 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:58:14,820 - discord_bot - INFO - info:69 - Loaded 0 setup channels from persistence
2025-07-11 18:58:14,821 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 0 setup channels.
2025-07-11 18:58:17,325 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 18:58:17,325 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 18:59:15,054 - discord_bot - INFO - info:69 - Created setup channel #server-setup in test
2025-07-11 18:59:15,054 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 18:59:15,058 - discord_bot - INFO - info:69 - Added setup channel mapping: Server 1391798815304060981 -> Channel 1393260059521449985
2025-07-11 18:59:15,687 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 18:59:15
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!setup"
Parsed Intent: setup_bot
Generated Actions: 1
Execution Status: SUCCESS
Result: Created setup channel #server-setup
=== END LOG ===
2025-07-11 18:59:26,148 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: Design a server for my gaming community
2025-07-11 18:59:26,494 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 18:59:26,513 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 164, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 18:59:26,537 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 18:59:26
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "Design a server for my gaming community"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:00:04,352 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: Create a support channel in a Help category
2025-07-11 19:00:04,413 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:04,425 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 164, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:04,437 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:00:04
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "Create a support channel in a Help category"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:00:12,023 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: What's missing from this server?
2025-07-11 19:00:12,086 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:12,099 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 164, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 206, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:00:12,111 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:00:12
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "What's missing from this server?"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:01:34,547 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 19:01:36,120 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 19:01:36,755 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 19:01:36,756 - discord_bot - INFO - info:69 - Loaded bot data from bot_data.json
2025-07-11 19:01:36,757 - discord_bot - INFO - info:69 - Loaded 1 setup channels from persistence
2025-07-11 19:01:36,757 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 19:01:39,384 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 19:01:39,385 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 19:02:10,462 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:02:10
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!clear"
Parsed Intent: reset_server
Generated Actions: 1
Execution Status: SUCCESS
Result: Deleted 1 channels, 0 categories, 0 roles
=== END LOG ===
2025-07-11 19:02:38,902 - discord_bot - INFO - info:69 - Test info message
2025-07-11 19:02:38,902 - discord_bot - WARNING - warning:73 - Test warning message
2025-07-11 19:02:38,903 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:02:38
Server: Test Server (ID: 12345)
User: TestUser#1234
Input: "test command"
Parsed Intent: test_intent
Generated Actions: 3
Execution Status: SUCCESS
Result: Test completed successfully
=== END LOG ===
2025-07-11 19:02:38,906 - discord_bot - INFO - info:69 - No existing data file found, starting with empty data
2025-07-11 19:02:38,912 - discord_bot - INFO - info:69 - Added setup channel mapping: Server 12345 -> Channel 67890
2025-07-11 19:02:38,930 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:38,933 - discord_bot - INFO - info:69 - Added setup channel mapping: Server 11111 -> Channel 22222
2025-07-11 19:02:38,951 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:38,953 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:38,975 - discord_bot - INFO - info:69 - Loaded bot data from test_data.json
2025-07-11 19:02:39,275 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,286 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,364 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,375 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,450 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,460 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,532 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,550 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,620 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,629 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,711 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,719 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,792 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,799 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,867 - discord_bot - ERROR - error:77 - Gemini API error: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:02:39,877 - discord_bot - ERROR - error:77 - Error in intent parsing: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 168, in parse_intent
    response = await self._generate_response(full_prompt)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\ai_service.py", line 210, in _generate_response
    response = self.model.generate_content(prompt)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\generativeai\generative_models.py", line 331, in generate_content
    response = self._client.generate_content(
        request,
        **request_options,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 835, in generate_content
    response = rpc(
        request,
    ...<2 lines>...
        metadata=metadata,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
        target,
    ...<3 lines>...
        on_error=on_error,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
        exc,
    ...<6 lines>...
        timeout,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 19:12:39,036 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 19:12:40,119 - discord_bot - INFO - info:69 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 19:12:40,120 - discord_bot - INFO - info:69 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 19:12:40,126 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 19:12:42,732 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 19:12:42,734 - discord_bot - INFO - info:69 - Loaded bot data from bot_data.json
2025-07-11 19:12:42,744 - discord_bot - INFO - info:69 - Loaded 1 setup channels from persistence
2025-07-11 19:12:42,745 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 19:12:45,233 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 19:12:45,234 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 19:13:44,491 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 19:13:45,509 - discord_bot - INFO - info:69 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 19:13:45,509 - discord_bot - INFO - info:69 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 19:13:45,514 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 19:13:46,150 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 19:13:46,152 - discord_bot - INFO - info:69 - Loaded bot data from bot_data.json
2025-07-11 19:13:46,152 - discord_bot - INFO - info:69 - Loaded 1 setup channels from persistence
2025-07-11 19:13:46,153 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 19:13:48,674 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 19:13:48,675 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 19:15:01,644 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: hello
2025-07-11 19:15:02,224 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:15:02,225 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:02
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "hello"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:15:02,780 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:02
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "hello"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:15:18,698 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a general channel
2025-07-11 19:15:19,011 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:15:19,011 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:19
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a general channel"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:15:19,971 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:19
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a general channel"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:15:33,091 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a voice channel called voice 1
2025-07-11 19:15:33,411 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:15:33,412 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:33
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a voice channel called voice 1"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:15:34,349 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:34
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a voice channel called voice 1"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:15:43,187 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: add emojis to the existing channels
2025-07-11 19:15:43,477 - discord_bot - INFO - info:69 - Successfully parsed intent: 0 actions generated
2025-07-11 19:15:43,478 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:15:43
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "add emojis to the existing channels"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:16:07,731 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:16:07
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!clear"
Parsed Intent: reset_server
Generated Actions: 2
Execution Status: SUCCESS
Result: Deleted 2 channels, 0 categories, 0 roles
=== END LOG ===
2025-07-11 19:16:29,470 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-11 19:16:29,841 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:16:29,842 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:16:29
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "design a discord server for a minecraft smp"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:16:40,164 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:16:40
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "design a discord server for a minecraft smp"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:17:13,213 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: enhance all channels with emojis before their name
2025-07-11 19:17:14,572 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 19:17:14,573 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:17:14
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "enhance all channels with emojis before their name"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:18:04,073 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a support category
2025-07-11 19:18:04,527 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:18:04,527 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:04
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:18:07,316 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:07
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:18:23,581 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: create a channel called tickets in the support category
2025-07-11 19:18:24,085 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:18:24,086 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:24
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a channel called tickets in the support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:18:25,246 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:25
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "create a channel called tickets in the support category"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:18:48,726 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: what is my server missing?
2025-07-11 19:18:56,235 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 19:18:56,235 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:18:56
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "what is my server missing?"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:22:27,063 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: adjust the permissions of members so they cant write in <#1393264418955202570>
2025-07-11 19:22:27,622 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 19:22:27,623 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:22:27
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "adjust the permissions of members so they cant write in <#1393264418955202570>"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:32:56,668 - discord_bot - INFO - info:69 - Starting Discord Server Management Bot...
2025-07-11 19:32:58,017 - discord_bot - INFO - info:69 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 19:32:58,018 - discord_bot - INFO - info:69 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 19:32:58,052 - discord_bot - INFO - info:69 - Loaded core commands
2025-07-11 19:32:58,654 - discord_bot - INFO - info:69 - Bot is starting up...
2025-07-11 19:32:58,658 - discord_bot - INFO - info:69 - Loaded bot data from bot_data.json
2025-07-11 19:32:58,659 - discord_bot - INFO - info:69 - Loaded 1 setup channels from persistence
2025-07-11 19:32:58,660 - discord_bot - INFO - info:69 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 19:33:01,150 - discord_bot - INFO - info:69 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 19:33:01,150 - discord_bot - INFO - info:69 - Connected to 1 servers
2025-07-11 19:35:35,478 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: adjust the permissions of members so they cant write in ⁠announcements
2025-07-11 19:35:36,230 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 19:35:36,231 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:35:36
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "adjust the permissions of members so they cant write in ⁠announcements"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:35:36,981 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:35:36
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "adjust the permissions of members so they cant write in ⁠announcements"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 19:35:45,088 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: what is my server missing?
2025-07-11 19:36:08,736 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 19:36:08,737 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:36:08
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "what is my server missing?"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:36:15,202 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: enhance all channels with emojis
2025-07-11 19:36:48,865 - discord_bot - INFO - info:69 - Successfully parsed intent: 2 actions generated
2025-07-11 19:36:48,866 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:36:48
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "enhance all channels with emojis"
Parsed Intent: ai_command
Generated Actions: 2
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 19:36:49,164 - discord_bot - ERROR - error:77 - Action execution error: Channel name, message ID, and emoji are required
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 38, in execute_actions
    result = await self._execute_single_action(action, guild)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 125, in _execute_single_action
    return await self._add_reaction(action, guild)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 573, in _add_reaction
    raise ValueError("Channel name, message ID, and emoji are required")
ValueError: Channel name, message ID, and emoji are required
2025-07-11 19:36:49,168 - discord_bot - ERROR - error:77 - Action execution error: Channel name, message ID, and emoji are required
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 38, in execute_actions
    result = await self._execute_single_action(action, guild)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 125, in _execute_single_action
    return await self._add_reaction(action, guild)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 573, in _add_reaction
    raise ValueError("Channel name, message ID, and emoji are required")
ValueError: Channel name, message ID, and emoji are required
2025-07-11 19:36:49,525 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 19:36:49
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "enhance all channels with emojis"
Parsed Intent: ai_command
Generated Actions: 2
Execution Status: FAILED
Result: Success: 0, Errors: 2
=== END LOG ===
2025-07-11 20:14:48,361 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: enhance all channels with emojis
2025-07-11 20:14:49,344 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 20:14:49,345 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 20:14:49
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "enhance all channels with emojis"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 20:15:03,109 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: add emojis to all channels
2025-07-11 20:15:25,610 - discord_bot - INFO - info:69 - Successfully parsed intent: 0 actions generated
2025-07-11 20:15:25,611 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 20:15:25
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "add emojis to all channels"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 20:15:48,790 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 20:15:48
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!clear"
Parsed Intent: reset_server
Generated Actions: 24
Execution Status: SUCCESS
Result: Deleted 14 channels, 5 categories, 5 roles
=== END LOG ===
2025-07-11 20:17:17,163 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: design a server for a hosting company
2025-07-11 20:17:17,616 - discord_bot - INFO - info:69 - Successfully parsed intent: 1 actions generated
2025-07-11 20:17:17,616 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 20:17:17
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "design a server for a hosting company"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 20:17:24,938 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 20:17:24
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "design a server for a hosting company"
Parsed Intent: ai_command
Generated Actions: 1
Execution Status: SUCCESS
Result: Success: 1, Errors: 0
=== END LOG ===
2025-07-11 20:18:14,627 - discord_bot - INFO - info:69 - Processing AI command from jima__gr in test: try to separate the server to 3 categories 
1 Company 
2 Support
3 Staff and developers only
2025-07-11 20:18:15,713 - discord_bot - ERROR - error:77 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 20:18:15,714 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 20:18:15
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "try to separate the server to 3 categories 
1 Company 
2 Support
3 Staff and developers only"
Parsed Intent: ai_command
Generated Actions: 0
Execution Status: PROCESSING
Result: N/A
=== END LOG ===
2025-07-11 20:34:25,545 - discord_bot - INFO - log_command:65 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-11 20:34:25
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!clear"
Parsed Intent: reset_server
Generated Actions: 17
Execution Status: SUCCESS
Result: Deleted 9 channels, 3 categories, 5 roles
=== END LOG ===
2025-07-11 20:57:29,504 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-11 20:57:30,669 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 20:57:30,669 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 20:57:30,726 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-11 20:57:32,936 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-11 20:57:32,938 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-11 20:57:32,938 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-11 20:57:32,942 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 20:57:37,054 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 20:57:37,054 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-11 21:00:04,175 - discord_bot - ERROR - error:79 - Error processing enhanced AI command: name 'time' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 321, in on_message
    start_time = time.time()
                 ^^^^
NameError: name 'time' is not defined. Did you forget to import 'time'?
2025-07-11 21:00:26,577 - discord_bot - ERROR - error:79 - Error processing enhanced AI command: name 'time' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 321, in on_message
    start_time = time.time()
                 ^^^^
NameError: name 'time' is not defined. Did you forget to import 'time'?
2025-07-11 21:05:06,961 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-11 21:05:08,035 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 21:05:08,036 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 21:05:08,050 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-11 21:05:08,737 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-11 21:05:08,739 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-11 21:05:08,740 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-11 21:05:08,740 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 21:05:11,205 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 21:05:11,206 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-11 21:05:59,064 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-11 21:06:00,099 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-11 21:06:00,099 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-11 21:06:00,110 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-11 21:06:00,692 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-11 21:06:00,694 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-11 21:06:00,695 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-11 21:06:00,695 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-11 21:06:03,230 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-11 21:06:03,231 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-11 21:11:57,621 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-11 21:11:59,075 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-11 21:11:59,075 - discord_bot - ERROR - error:79 - Error processing enhanced AI command: 'BotLogger' object has no attribute 'log_ai_parsing'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 350, in on_message
    bot_logger.log_ai_parsing(
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BotLogger' object has no attribute 'log_ai_parsing'
2025-07-13 01:08:16,877 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-13 01:08:18,744 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-13 01:08:18,745 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-13 01:08:18,775 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-13 01:08:19,558 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-13 01:08:19,570 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-13 01:08:19,574 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-13 01:08:19,575 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-13 01:08:22,126 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-13 01:08:22,127 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-13 01:14:57,262 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: Create a test channel
2025-07-13 01:14:58,259 - discord_bot - INFO - info:71 - Successfully parsed multi-intent: 1 actions, 1 intents
2025-07-13 01:14:58,259 - discord_bot - ERROR - error:79 - Error processing enhanced AI command: 'BotLogger' object has no attribute 'log_ai_parsing'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 350, in on_message
    bot_logger.log_ai_parsing(
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BotLogger' object has no attribute 'log_ai_parsing'
2025-07-13 01:20:35,521 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-13 01:20:35,522 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-13 01:20:36,426 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockGuild' object has no attribute 'created_at'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\tests\..\src\services\server_context.py", line 29, in get_server_context
    "created_at": guild.created_at.isoformat(),
                  ^^^^^^^^^^^^^^^^
AttributeError: 'MockGuild' object has no attribute 'created_at'
2025-07-13 01:20:37,151 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:20:37,166 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockGuild' object has no attribute 'created_at'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\tests\..\src\services\server_context.py", line 29, in get_server_context
    "created_at": guild.created_at.isoformat(),
                  ^^^^^^^^^^^^^^^^
AttributeError: 'MockGuild' object has no attribute 'created_at'
2025-07-13 01:20:48,746 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:20:48,867 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockGuild' object has no attribute 'created_at'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\tests\..\src\services\server_context.py", line 29, in get_server_context
    "created_at": guild.created_at.isoformat(),
                  ^^^^^^^^^^^^^^^^
AttributeError: 'MockGuild' object has no attribute 'created_at'
2025-07-13 01:21:23,063 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:21:23,070 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockGuild' object has no attribute 'created_at'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\tests\..\src\services\server_context.py", line 29, in get_server_context
    "created_at": guild.created_at.isoformat(),
                  ^^^^^^^^^^^^^^^^
AttributeError: 'MockGuild' object has no attribute 'created_at'
2025-07-13 01:22:00,804 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:22:00,934 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockGuild' object has no attribute 'created_at'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\tests\..\src\services\server_context.py", line 29, in get_server_context
    "created_at": guild.created_at.isoformat(),
                  ^^^^^^^^^^^^^^^^
AttributeError: 'MockGuild' object has no attribute 'created_at'
2025-07-13 01:22:41,703 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:22:41,713 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockGuild' object has no attribute 'created_at'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\tests\..\src\services\server_context.py", line 29, in get_server_context
    "created_at": guild.created_at.isoformat(),
                  ^^^^^^^^^^^^^^^^
AttributeError: 'MockGuild' object has no attribute 'created_at'
2025-07-13 01:23:18,726 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:23:53,952 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockCategory' object has no attribute 'position'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\src\services\server_context.py", line 48, in get_server_context
    "position": category.position,
                ^^^^^^^^^^^^^^^^^
AttributeError: 'MockCategory' object has no attribute 'position'
2025-07-13 01:25:17,211 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockCategory' object has no attribute 'category'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\src\services\server_context.py", line 60, in get_server_context
    if channel.category is None and not isinstance(channel, discord.CategoryChannel):
       ^^^^^^^^^^^^^^^^
AttributeError: 'MockCategory' object has no attribute 'category'
2025-07-13 01:25:56,268 - discord_bot - ERROR - error:79 - Error gathering server context: 'MockCategory' object has no attribute 'type'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\src\services\server_context.py", line 61, in get_server_context
    channel_data = ServerContextService._get_channel_data(channel)
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\tests\..\src\services\server_context.py", line 113, in _get_channel_data
    "type": channel.type.name
            ^^^^^^^^^^^^
AttributeError: 'MockCategory' object has no attribute 'type'
2025-07-13 01:26:35,469 - discord_bot - INFO - info:71 - Loaded context memory for 1 servers
2025-07-13 01:26:58,548 - discord_bot - INFO - info:71 - Loaded context memory for 1 servers
2025-07-13 01:27:32,180 - discord_bot - INFO - info:71 - Loaded context memory for 1 servers
2025-07-13 01:29:12,928 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-13 01:29:13,912 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-13 01:29:13,913 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-13 01:29:13,929 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-13 01:29:14,674 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-13 01:29:14,676 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-13 01:29:14,678 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-13 01:29:14,678 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-13 01:29:17,248 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-13 01:29:17,248 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-13 01:29:34,515 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-13 01:29:35,396 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:29:35,401 - discord_bot - ERROR - error:79 - Error processing enhanced AI command: 'BotLogger' object has no attribute 'log_ai_parsing'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\core\commands.py", line 350, in on_message
    bot_logger.log_ai_parsing(
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BotLogger' object has no attribute 'log_ai_parsing'
2025-07-13 01:31:16,826 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-13 01:31:18,726 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-13 01:31:18,726 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-13 01:31:18,742 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-13 01:31:19,432 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-13 01:31:19,434 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-13 01:31:19,435 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-13 01:31:19,436 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-13 01:31:21,896 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-13 01:31:21,897 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-13 01:31:31,298 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-13 01:31:33,158 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:31:33,158 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:31:33,159 - discord_bot - INFO - log_ai_parsing:146 - Request: design a discord server for a minecraft smp
2025-07-13 01:31:33,159 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 1.854s
2025-07-13 01:31:33,160 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 0
2025-07-13 01:31:33,160 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 0
2025-07-13 01:31:33,160 - discord_bot - INFO - log_ai_parsing:150 - Status: FAILED
2025-07-13 01:31:33,161 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:35:41,328 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-13 01:35:42,362 - discord_bot - INFO - info:71 - Testing Groq API connection...
2025-07-13 01:35:42,736 - discord_bot - INFO - info:71 - API connection test successful: OK
2025-07-13 01:35:42,737 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-13 01:35:42,737 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-13 01:35:42,753 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-13 01:35:43,334 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-13 01:35:43,336 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-13 01:35:43,337 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-13 01:35:43,337 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-13 01:35:50,125 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-13 01:35:50,125 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-13 01:35:58,212 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-13 01:35:58,808 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:35:58,809 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:35:58,809 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:35:58,809 - discord_bot - INFO - log_ai_parsing:146 - Request: design a discord server for a minecraft smp
2025-07-13 01:35:58,810 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 0.594s
2025-07-13 01:35:58,810 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:35:58,811 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:35:58,811 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:35:58,811 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:35:59,126 - discord_bot - ERROR - error:79 - Action execution error: Unknown action type: send_message
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 41, in execute_actions
    result = await self._execute_single_action(action, guild)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 187, in _execute_single_action
    raise ValueError(f"Unknown action type: {action_type}")
ValueError: Unknown action type: send_message
2025-07-13 01:35:59,361 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:35:59,361 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:35:59,362 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:35:59.361156
2025-07-13 01:35:59,362 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:35:59,362 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:35:59,363 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: "design a discord server for a minecraft smp"
2025-07-13 01:35:59,363 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Service unavailable']
2025-07-13 01:35:59,363 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:35:59,367 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:35:59,368 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: send_message - ❌ failed
2025-07-13 01:35:59,369 - discord_bot - INFO - log_multi_step_operation:122 -   Error: Failed to execute send_message: Unknown action type: send_message
2025-07-13 01:35:59,369 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:35:59,370 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 0/1 actions completed
2025-07-13 01:35:59,370 - discord_bot - INFO - log_multi_step_operation:134 - Failed Actions: 1
2025-07-13 01:35:59,370 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 0.92 seconds
2025-07-13 01:35:59,371 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:37:20,473 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-13 01:37:21,842 - discord_bot - INFO - info:71 - Testing Groq API connection...
2025-07-13 01:37:22,188 - discord_bot - INFO - info:71 - API connection test successful: OK
2025-07-13 01:37:22,188 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-13 01:37:22,189 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-13 01:37:22,224 - discord_bot - INFO - info:71 - Loaded context memory for 1 servers
2025-07-13 01:37:22,224 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-13 01:37:22,834 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-13 01:37:22,835 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-13 01:37:22,836 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-13 01:37:22,836 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-13 01:37:25,291 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-13 01:37:25,291 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-13 01:37:34,341 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-13 01:37:34,967 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:37:34,969 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:37:34,969 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:37:34,969 - discord_bot - INFO - log_ai_parsing:146 - Request: design a discord server for a minecraft smp
2025-07-13 01:37:34,970 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 0.628s
2025-07-13 01:37:34,970 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:37:34,970 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:37:34,970 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:37:34,971 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:37:35,459 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:37:35,460 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:37:35,462 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:37:35.459647
2025-07-13 01:37:35,462 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:37:35,463 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:37:35,463 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: "design a discord server for a minecraft smp"
2025-07-13 01:37:35,463 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Service unavailable']
2025-07-13 01:37:35,464 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:37:35,464 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:37:35,464 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: send_message - ✅ completed
2025-07-13 01:37:35,465 - discord_bot - INFO - log_multi_step_operation:120 -   Result: 📢 **System Message**: I'm currently experiencing technical difficulties. Please try:
• Simpler commands
• Rephrasing your request
• Contacting an administrator

Example: 'create a channel' or 'analyze my server'
2025-07-13 01:37:35,466 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:37:35,467 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 1/1 actions completed
2025-07-13 01:37:35,467 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 0.87 seconds
2025-07-13 01:37:35,467 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:38:17,158 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-13 01:38:17,816 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:38:17,817 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:38:17,818 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:38:17,818 - discord_bot - INFO - log_ai_parsing:146 - Request: design a discord server for a minecraft smp
2025-07-13 01:38:17,818 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 0.637s
2025-07-13 01:38:17,819 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:38:17,819 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:38:17,830 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:38:17,830 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:38:18,457 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:38:18,466 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:38:18,467 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:38:18.457789
2025-07-13 01:38:18,467 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:38:18,467 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:38:18,468 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: "design a discord server for a minecraft smp"
2025-07-13 01:38:18,476 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Service unavailable']
2025-07-13 01:38:18,477 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:38:18,478 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:38:18,479 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: send_message - ✅ completed
2025-07-13 01:38:18,480 - discord_bot - INFO - log_multi_step_operation:120 -   Result: 📢 **System Message**: I'm currently experiencing technical difficulties. Please try:
• Simpler commands
• Rephrasing your request
• Contacting an administrator

Example: 'create a channel' or 'analyze my server'
2025-07-13 01:38:18,481 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:38:18,483 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 1/1 actions completed
2025-07-13 01:38:18,484 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 1.03 seconds
2025-07-13 01:38:18,484 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:38:40,067 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: "create a channel"
2025-07-13 01:38:41,833 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:38:41,833 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:38:41,834 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:38:41,834 - discord_bot - INFO - log_ai_parsing:146 - Request: "create a channel"
2025-07-13 01:38:41,834 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 1.767s
2025-07-13 01:38:41,835 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:38:41,835 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:38:41,835 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:38:41,836 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:38:42,132 - discord_bot - ERROR - error:79 - Action execution error: Unknown action type: create_text_channel
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 41, in execute_actions
    result = await self._execute_single_action(action, guild)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 191, in _execute_single_action
    raise ValueError(f"Unknown action type: {action_type}")
ValueError: Unknown action type: create_text_channel
2025-07-13 01:38:42,399 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:38:42,400 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:38:42,400 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:38:42.399577
2025-07-13 01:38:42,400 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:38:42,400 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:38:42,401 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: ""create a channel""
2025-07-13 01:38:42,401 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Create channel']
2025-07-13 01:38:42,401 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:38:42,402 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:38:42,402 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: create_text_channel - ❌ failed
2025-07-13 01:38:42,402 - discord_bot - INFO - log_multi_step_operation:122 -   Error: Failed to execute create_text_channel: Unknown action type: create_text_channel
2025-07-13 01:38:42,403 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:38:42,403 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 0/1 actions completed
2025-07-13 01:38:42,403 - discord_bot - INFO - log_multi_step_operation:134 - Failed Actions: 1
2025-07-13 01:38:42,404 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 2.07 seconds
2025-07-13 01:38:42,404 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:38:54,395 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: create a channel called general
2025-07-13 01:39:22,073 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:39:22,074 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:39:22,074 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:39:22,074 - discord_bot - INFO - log_ai_parsing:146 - Request: create a channel called general
2025-07-13 01:39:22,074 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 27.678s
2025-07-13 01:39:22,075 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:39:22,075 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:39:22,075 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:39:22,076 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:39:22,432 - discord_bot - ERROR - error:79 - Action execution error: Unknown action type: create_text_channel
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 41, in execute_actions
    result = await self._execute_single_action(action, guild)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 191, in _execute_single_action
    raise ValueError(f"Unknown action type: {action_type}")
ValueError: Unknown action type: create_text_channel
2025-07-13 01:39:22,688 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:39:22,690 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:39:22,690 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:39:22.688000
2025-07-13 01:39:22,690 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:39:22,691 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:39:22,692 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: "create a channel called general"
2025-07-13 01:39:22,692 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Create channel']
2025-07-13 01:39:22,694 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:39:22,694 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:39:22,695 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: create_text_channel - ❌ failed
2025-07-13 01:39:22,695 - discord_bot - INFO - log_multi_step_operation:122 -   Error: Failed to execute create_text_channel: Unknown action type: create_text_channel
2025-07-13 01:39:22,696 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:39:22,696 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 0/1 actions completed
2025-07-13 01:39:22,696 - discord_bot - INFO - log_multi_step_operation:134 - Failed Actions: 1
2025-07-13 01:39:22,697 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 28.04 seconds
2025-07-13 01:39:22,697 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:41:51,370 - discord_bot - INFO - info:71 - Starting Discord Server Management Bot...
2025-07-13 01:41:52,383 - discord_bot - INFO - info:71 - Testing Groq API connection...
2025-07-13 01:41:52,760 - discord_bot - INFO - info:71 - API connection test successful: OK
2025-07-13 01:41:52,760 - discord_bot - INFO - info:71 - AI Service initialized with Groq model: meta-llama/llama-4-maverick-17b-128e-instruct
2025-07-13 01:41:52,761 - discord_bot - INFO - info:71 - Model purpose: Real-time Discord responses, JSON generation
2025-07-13 01:41:52,776 - discord_bot - INFO - info:71 - Loaded context memory for 1 servers
2025-07-13 01:41:52,777 - discord_bot - INFO - info:71 - Loaded core commands
2025-07-13 01:41:53,278 - discord_bot - INFO - info:71 - Bot is starting up...
2025-07-13 01:41:53,280 - discord_bot - INFO - info:71 - Loaded bot data from bot_data.json
2025-07-13 01:41:53,281 - discord_bot - INFO - info:71 - Loaded 1 setup channels from persistence
2025-07-13 01:41:53,281 - discord_bot - INFO - info:71 - Bot setup complete. Loaded 1 setup channels.
2025-07-13 01:41:55,925 - discord_bot - INFO - info:71 - Bot is ready! Logged in as Phalanx AI#6458 (ID: 1373657745403940946)
2025-07-13 01:41:55,926 - discord_bot - INFO - info:71 - Connected to 1 servers
2025-07-13 01:42:16,509 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-13 01:42:17,653 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:42:17,654 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:42:17,654 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:42:17,654 - discord_bot - INFO - log_ai_parsing:146 - Request: design a discord server for a minecraft smp
2025-07-13 01:42:17,655 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 1.145s
2025-07-13 01:42:17,655 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:42:17,655 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:42:17,655 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:42:17,656 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:42:17,921 - discord_bot - ERROR - error:79 - Action execution error: Unknown template: minecraft
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 41, in execute_actions
    result = await self._execute_single_action(action, guild)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 121, in _execute_single_action
    results = await self.channel_manager.create_channel_with_template(guild, template_name, category_name)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\channel_manager.py", line 47, in create_channel_with_template
    raise ValueError(f"Unknown template: {template_name}")
ValueError: Unknown template: minecraft
2025-07-13 01:42:18,157 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:42:18,157 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:42:18,157 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:42:18.157170
2025-07-13 01:42:18,158 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:42:18,158 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:42:18,158 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: "design a discord server for a minecraft smp"
2025-07-13 01:42:18,158 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Design minecraft server']
2025-07-13 01:42:18,158 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:42:18,159 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:42:18,159 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: create_channel_template - ❌ failed
2025-07-13 01:42:18,159 - discord_bot - INFO - log_multi_step_operation:122 -   Error: Failed to execute create_channel_template: Unknown template: minecraft
2025-07-13 01:42:18,160 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:42:18,160 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 0/1 actions completed
2025-07-13 01:42:18,161 - discord_bot - INFO - log_multi_step_operation:134 - Failed Actions: 1
2025-07-13 01:42:18,161 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 1.42 seconds
2025-07-13 01:42:18,162 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:42:37,995 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: create a channel called general
2025-07-13 01:42:41,662 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:42:41,662 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:42:41,662 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:42:41,663 - discord_bot - INFO - log_ai_parsing:146 - Request: create a channel called general
2025-07-13 01:42:41,663 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 3.667s
2025-07-13 01:42:41,664 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:42:41,664 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:42:41,664 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:42:41,665 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:42:42,422 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:42:42,422 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:42:42,422 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:42:42.422017
2025-07-13 01:42:42,423 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:42:42,423 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:42:42,424 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: "create a channel called general"
2025-07-13 01:42:42,424 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Create channel']
2025-07-13 01:42:42,424 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:42:42,425 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:42:42,425 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: create_channel - ✅ completed
2025-07-13 01:42:42,426 - discord_bot - INFO - log_multi_step_operation:120 -   Result: Created text channel #general
2025-07-13 01:42:42,426 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:42:42,427 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 1/1 actions completed
2025-07-13 01:42:42,427 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 4.19 seconds
2025-07-13 01:42:42,427 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:42:52,105 - discord_bot - INFO - info:71 - Processing enhanced AI command from jima__gr in test: design a discord server for a minecraft smp
2025-07-13 01:43:22,899 - discord_bot - ERROR - error:79 - Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)
2025-07-13 01:43:22,900 - discord_bot - INFO - info:71 - Using local fallback command processing
2025-07-13 01:43:22,900 - discord_bot - INFO - log_ai_parsing:145 - === AI PARSING LOG ===
2025-07-13 01:43:22,901 - discord_bot - INFO - log_ai_parsing:146 - Request: design a discord server for a minecraft smp
2025-07-13 01:43:22,901 - discord_bot - INFO - log_ai_parsing:147 - Parsing Time: 30.795s
2025-07-13 01:43:22,902 - discord_bot - INFO - log_ai_parsing:148 - Intents Detected: 1
2025-07-13 01:43:22,902 - discord_bot - INFO - log_ai_parsing:149 - Actions Generated: 1
2025-07-13 01:43:22,902 - discord_bot - INFO - log_ai_parsing:150 - Status: SUCCESS
2025-07-13 01:43:22,903 - discord_bot - INFO - log_ai_parsing:151 - ========================================
2025-07-13 01:43:23,316 - discord_bot - ERROR - error:79 - Action execution error: Unknown template: minecraft
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 41, in execute_actions
    result = await self._execute_single_action(action, guild)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\action_dispatcher.py", line 121, in _execute_single_action
    results = await self.channel_manager.create_channel_with_template(guild, template_name, category_name)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\ai bot\src\services\channel_manager.py", line 47, in create_channel_with_template
    raise ValueError(f"Unknown template: {template_name}")
ValueError: Unknown template: minecraft
2025-07-13 01:43:23,778 - discord_bot - INFO - log_multi_step_operation:99 - ================================================================================
2025-07-13 01:43:23,778 - discord_bot - INFO - log_multi_step_operation:100 - MULTI-STEP OPERATION LOG
2025-07-13 01:43:23,779 - discord_bot - INFO - log_multi_step_operation:101 - Timestamp: 2025-07-13T01:43:23.778386
2025-07-13 01:43:23,779 - discord_bot - INFO - log_multi_step_operation:102 - Server: test (ID: 1391798815304060981)
2025-07-13 01:43:23,780 - discord_bot - INFO - log_multi_step_operation:103 - User: 1326101557216935987
2025-07-13 01:43:23,780 - discord_bot - INFO - log_multi_step_operation:104 - Original Request: "design a discord server for a minecraft smp"
2025-07-13 01:43:23,781 - discord_bot - INFO - log_multi_step_operation:105 - Parsed Intents: ['Design minecraft server']
2025-07-13 01:43:23,781 - discord_bot - INFO - log_multi_step_operation:106 - Planned Actions: 1
2025-07-13 01:43:23,782 - discord_bot - INFO - log_multi_step_operation:109 - 
EXECUTION SEQUENCE:
2025-07-13 01:43:23,782 - discord_bot - INFO - log_multi_step_operation:117 - Step 1: create_channel_template - ❌ failed
2025-07-13 01:43:23,783 - discord_bot - INFO - log_multi_step_operation:122 -   Error: Failed to execute create_channel_template: Unknown template: minecraft
2025-07-13 01:43:23,784 - discord_bot - INFO - log_multi_step_operation:125 - 
FINAL SUMMARY:
2025-07-13 01:43:23,786 - discord_bot - INFO - log_multi_step_operation:132 - Success Rate: 0/1 actions completed
2025-07-13 01:43:23,786 - discord_bot - INFO - log_multi_step_operation:134 - Failed Actions: 1
2025-07-13 01:43:23,786 - discord_bot - INFO - log_multi_step_operation:137 - Execution Time: 31.22 seconds
2025-07-13 01:43:23,786 - discord_bot - INFO - log_multi_step_operation:138 - ================================================================================
2025-07-13 01:47:16,339 - discord_bot - INFO - log_command:67 - === BOT PROCESSING LOG ===
Timestamp: 2025-07-13 01:47:16
Server: test (ID: 1391798815304060981)
User: jima__gr
Input: "!clear"
Parsed Intent: reset_server
Generated Actions: 1
Execution Status: SUCCESS
Result: Deleted 1 channels, 0 categories, 0 roles
=== END LOG ===
