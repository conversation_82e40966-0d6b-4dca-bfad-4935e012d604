"""
Main entry point for the Discord Server Management Bot.
"""
import asyncio
import sys
import os

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import config
from src.core.bot import DiscordServerBot
from src.utils.logger import bot_logger

async def main():
    """Main function to start the bot."""
    bot_logger.info("Starting Discord Server Management Bot...")
    
    # Create bot instance
    bot = DiscordServerBot()
    
    try:
        # Load core commands
        await bot.load_extension('src.core.commands')
        bot_logger.info("Loaded core commands")
        
        # Start the bot
        await bot.start(config.DISCORD_TOKEN)
        
    except Exception as e:
        bot_logger.error(f"Failed to start bot: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        bot_logger.info("Bot shutdown requested by user")
    except Exception as e:
        bot_logger.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)
