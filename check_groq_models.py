"""
<PERSON><PERSON><PERSON> to check available Groq models and test their performance for Discord bot tasks.
"""
import asyncio
from groq import Groq
import time
import json
from typing import List, Dict, Any
import config

def list_groq_models():
    """List all available Groq models."""
    print("🔍 Checking available Groq models...\n")
    
    try:
        client = Groq(api_key=config.GROQ_API_KEY)  # Using the gsk_ key
        models = client.models.list()
        
        available_models = []
        for model in models.data:
            model_info = {
                'id': model.id,
                'object': model.object,
                'created': model.created,
                'owned_by': model.owned_by,
                'active': getattr(model, 'active', True),
                'context_window': getattr(model, 'context_window', 'Unknown'),
                'public_apps': getattr(model, 'public_apps', None)
            }
            available_models.append(model_info)
            
            print(f"📋 Model: {model.id}")
            print(f"   Object: {model.object}")
            print(f"   Created: {model.created}")
            print(f"   Owned by: {model.owned_by}")
            print(f"   Context Window: {model_info['context_window']}")
            if hasattr(model, 'active'):
                print(f"   Active: {model.active}")
            print("-" * 60)
        
        return available_models
        
    except Exception as e:
        print(f"❌ Error listing models: {e}")
        return []

def test_groq_model_performance(model_id: str, test_prompts: List[str]) -> Dict[str, Any]:
    """Test a specific Groq model's performance on Discord bot tasks."""
    print(f"\n🧪 Testing model: {model_id}")
    
    try:
        client = Groq(api_key=config.GROQ_API_KEY)
        results = {
            'model_id': model_id,
            'test_results': [],
            'average_response_time': 0,
            'success_rate': 0,
            'total_tokens_used': 0
        }
        
        total_time = 0
        successful_tests = 0
        total_tokens = 0
        
        for i, prompt in enumerate(test_prompts):
            print(f"  Test {i+1}/{len(test_prompts)}: {prompt[:50]}...")
            
            start_time = time.time()
            try:
                response = client.chat.completions.create(
                    model=model_id,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=1000,
                    temperature=0.1  # Low temperature for consistent JSON output
                )
                end_time = time.time()
                
                response_time = end_time - start_time
                total_time += response_time
                successful_tests += 1
                
                response_text = response.choices[0].message.content
                tokens_used = response.usage.total_tokens if response.usage else 0
                total_tokens += tokens_used
                
                test_result = {
                    'prompt': prompt,
                    'response_time': response_time,
                    'response_length': len(response_text) if response_text else 0,
                    'tokens_used': tokens_used,
                    'success': True,
                    'response_preview': response_text[:100] + "..." if response_text and len(response_text) > 100 else response_text
                }
                
                print(f"    ✅ Success ({response_time:.2f}s, {test_result['response_length']} chars, {tokens_used} tokens)")
                
            except Exception as e:
                test_result = {
                    'prompt': prompt,
                    'response_time': 0,
                    'response_length': 0,
                    'tokens_used': 0,
                    'success': False,
                    'error': str(e)
                }
                print(f"    ❌ Failed: {str(e)[:50]}...")
            
            results['test_results'].append(test_result)
        
        results['average_response_time'] = total_time / len(test_prompts) if test_prompts else 0
        results['success_rate'] = (successful_tests / len(test_prompts)) * 100 if test_prompts else 0
        results['total_tokens_used'] = total_tokens
        
        print(f"  📊 Results: {successful_tests}/{len(test_prompts)} successful, avg {results['average_response_time']:.2f}s, {total_tokens} tokens")
        
        return results
        
    except Exception as e:
        print(f"  ❌ Model test failed: {e}")
        return {
            'model_id': model_id,
            'error': str(e),
            'success_rate': 0
        }

def recommend_best_groq_model():
    """Test available Groq models and recommend the best one for Discord bot tasks."""
    print("🎯 Discord Bot Groq Model Recommendation System\n")
    
    # Test prompts specifically designed for Discord bot tasks
    test_prompts = [
        """Convert this Discord command to JSON: "Create a support channel"

Respond with JSON only:
{
  "actions": [{"type": "create_channel", "name": "support", "channel_type": "text"}],
  "summary": "Creating support channel"
}""",
        
        """Convert this Discord command to JSON: "Design a gaming server"

Respond with JSON only:
{
  "actions": [{"type": "apply_server_template", "template_name": "gaming_community"}],
  "summary": "Applying gaming community template"
}""",
        
        """Convert this Discord command to JSON: "Delete all test channels"

Respond with JSON only:
{
  "actions": [{"type": "bulk_delete_channels", "pattern": "test"}],
  "summary": "Deleting channels with 'test' in name"
}"""
    ]
    
    # List available models
    models = list_groq_models()
    
    if not models:
        print("❌ No models available or API key invalid")
        return
    
    # Test each model
    model_results = []
    for model_info in models:
        model_id = model_info['id']
        results = test_groq_model_performance(model_id, test_prompts)
        model_results.append(results)
    
    # Analyze results and make recommendation
    print("\n" + "="*60)
    print("📊 GROQ MODEL PERFORMANCE ANALYSIS")
    print("="*60)
    
    best_model = None
    best_score = 0
    
    for result in model_results:
        if 'error' in result:
            print(f"\n❌ {result['model_id']}: Failed to test")
            continue
        
        # Calculate composite score (Groq is optimized for speed)
        success_weight = 0.5
        speed_weight = 0.4
        efficiency_weight = 0.1
        
        success_score = result['success_rate']
        speed_score = max(0, 100 - (result['average_response_time'] * 20))  # Groq should be very fast
        efficiency_score = max(0, 100 - (result['total_tokens_used'] / len(test_prompts) / 10))  # Token efficiency
        
        composite_score = (success_score * success_weight + 
                          speed_score * speed_weight + 
                          efficiency_score * efficiency_weight)
        
        print(f"\n🤖 {result['model_id']}")
        print(f"   Success Rate: {result['success_rate']:.1f}%")
        print(f"   Avg Response Time: {result['average_response_time']:.2f}s")
        print(f"   Avg Tokens/Request: {result['total_tokens_used'] / len(test_prompts):.0f}")
        print(f"   Composite Score: {composite_score:.1f}")
        
        if composite_score > best_score:
            best_score = composite_score
            best_model = result
    
    # Make recommendation
    print("\n" + "="*60)
    print("🏆 RECOMMENDATION")
    print("="*60)
    
    if best_model:
        print(f"\n✅ RECOMMENDED MODEL: {best_model['model_id']}")
        print(f"   Overall Score: {best_score:.1f}/100")
        print(f"   Success Rate: {best_model['success_rate']:.1f}%")
        print(f"   Average Response Time: {best_model['average_response_time']:.2f}s")
        print(f"   Average Tokens: {best_model['total_tokens_used'] / len(test_prompts):.0f}")
        
        print(f"\n📝 REASONING:")
        print(f"   This model provides the best balance of:")
        print(f"   • Reliability ({best_model['success_rate']:.1f}% success rate)")
        print(f"   • Speed ({best_model['average_response_time']:.2f}s average response)")
        print(f"   • Token efficiency")
        
        print(f"\n🔧 TO USE THIS MODEL:")
        print(f"   Update src/services/ai_service.py to use Groq API")
        print(f"   Model ID: '{best_model['model_id']}'")
        
    else:
        print("❌ No suitable model found. Check API key and try again.")
    
    return best_model

if __name__ == "__main__":
    print("🚀 Groq Model Analysis for Discord Bot")
    print("="*50)
    
    try:
        recommend_best_groq_model()
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nKnown Groq models (as of 2024):")
        print("• llama-3.1-405b-reasoning")
        print("• llama-3.1-70b-versatile") 
        print("• llama-3.1-8b-instant")
        print("• mixtral-8x7b-32768")
        print("• gemma-7b-it")
        print("\nGroq is optimized for ultra-fast inference!")
