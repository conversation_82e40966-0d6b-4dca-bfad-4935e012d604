"""
AI service for natural language processing using Gemini API.
"""
import json
import google.generativeai as genai
from typing import Dict, List, Any, Optional
import config
from src.utils.logger import bot_logger

class AIService:
    """Service for processing natural language commands using Gemini AI."""
    
    def __init__(self):
        # Configure Gemini
        genai.configure(api_key=config.GEMINI_API_KEY)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # System prompt for intent parsing
        self.system_prompt = """
You are an AI assistant that converts natural language Discord server management commands into structured JSON actions.

Your task is to analyze user input and generate a JSON response with two parts:
1. "actions": An array of specific actions to execute
2. "summary": A brief user-friendly description of what will be done

AVAILABLE ACTION TYPES:

BASIC ACTIONS:
- create_channel: Create text/voice channels
- create_category: Create channel categories
- delete_channel: Delete channels
- delete_category: Delete categories
- create_role: Create roles
- delete_role: Delete roles
- modify_permissions: Change channel/role permissions
- analyze_server: Analyze current server structure

ADVANCED CHANNEL ACTIONS:
- create_channel_template: Create multiple channels from templates (gaming_community, professional_workspace, study_group)
- organize_channels: Automatically organize uncategorized channels into logical categories
- duplicate_channel: Copy a channel with all its settings
- bulk_delete_channels: Delete multiple channels matching a pattern

ADVANCED ROLE ACTIONS:
- create_role_hierarchy: Create complete role hierarchies (gaming_community, professional_workspace, study_group)
- assign_role_colors: Apply color schemes to roles (rainbow, professional, warm)
- cleanup_roles: Remove unused roles with no members
- organize_role_hierarchy: Organize roles by permission level
- bulk_assign_role: Assign roles to members based on criteria (no_roles, new_members)

TEMPLATE ACTIONS:
- apply_server_template: Apply complete server templates (gaming_community, professional_workspace, educational)

ACTION SCHEMA:
{
  "type": "action_type",
  "name": "item_name", 
  "channel_type": "text|voice|stage", // for create_channel
  "category": "category_name", // optional, for channel placement
  "permissions": {
    "role_name": ["permission1", "permission2"],
    "@everyone": ["view_channel"]
  },
  "reason": "explanation for this action"
}

PERMISSION TYPES:
- view_channel, send_messages, read_message_history
- manage_messages, manage_channels, manage_roles
- connect, speak, mute_members (voice channels)

RULES:
1. Always generate clean, simple names (no "called-" prefixes)
2. Use appropriate permissions for channel types
3. Group related channels in categories when logical
4. For complex requests, break into multiple actions
5. For analysis requests, use analyze_server action type
6. Be conservative with permissions - start restrictive

EXAMPLES:

Input: "Create a support channel"
Output: {
  "actions": [
    {
      "type": "create_channel",
      "channel_type": "text", 
      "name": "support",
      "category": "Help",
      "permissions": {
        "@everyone": ["view_channel", "send_messages"],
        "Moderator": ["view_channel", "send_messages", "manage_messages"]
      },
      "reason": "User requested support channel creation"
    }
  ],
  "summary": "Creating #support channel in Help category"
}

Input: "Design a server for my gaming community"
Output: {
  "actions": [
    {
      "type": "apply_server_template",
      "template_name": "gaming_community",
      "reason": "Apply complete gaming community template with roles, channels, and permissions"
    }
  ],
  "summary": "Applying complete gaming community template with organized roles, channels, and permissions"
}

Input: "Set up a professional workspace"
Output: {
  "actions": [
    {
      "type": "apply_server_template",
      "template_name": "professional_workspace",
      "reason": "Apply professional workspace template for business use"
    }
  ],
  "summary": "Creating professional workspace with business-appropriate structure and roles"
}

Input: "Organize my messy server"
Output: {
  "actions": [
    {
      "type": "organize_channels",
      "reason": "Organize uncategorized channels into logical categories"
    },
    {
      "type": "organize_role_hierarchy",
      "reason": "Organize roles by permission level"
    },
    {
      "type": "cleanup_roles",
      "reason": "Remove unused roles with no members"
    }
  ],
  "summary": "Organizing server structure by categorizing channels and cleaning up roles"
}

Now process the user's command and respond with valid JSON only.
"""
    
    async def parse_intent(self, user_input: str, server_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Parse natural language input into structured actions.
        
        Args:
            user_input: The user's natural language command
            server_context: Optional context about current server state
            
        Returns:
            Dictionary with 'actions' and 'summary' keys
        """
        try:
            # Prepare the prompt with context
            context_info = ""
            if server_context:
                context_info = f"\nCURRENT SERVER CONTEXT:\n{json.dumps(server_context, indent=2)}\n"
            
            full_prompt = f"{self.system_prompt}{context_info}\nUSER COMMAND: {user_input}"
            
            bot_logger.debug(f"Sending prompt to Gemini: {user_input}")
            
            # Generate response
            response = await self._generate_response(full_prompt)
            
            # Parse JSON response
            try:
                parsed_response = json.loads(response)
                
                # Validate response structure
                if not isinstance(parsed_response, dict):
                    raise ValueError("Response is not a dictionary")
                
                if 'actions' not in parsed_response:
                    raise ValueError("Response missing 'actions' key")
                
                if 'summary' not in parsed_response:
                    parsed_response['summary'] = "Processing your request..."
                
                if not isinstance(parsed_response['actions'], list):
                    raise ValueError("Actions must be a list")
                
                bot_logger.info(f"Successfully parsed intent: {len(parsed_response['actions'])} actions generated")
                return parsed_response
                
            except json.JSONDecodeError as e:
                bot_logger.error(f"Failed to parse JSON response: {e}")
                bot_logger.debug(f"Raw response: {response}")
                
                # Return fallback response
                return {
                    "actions": [],
                    "summary": "I couldn't understand that command. Please try rephrasing it."
                }
                
        except Exception as e:
            bot_logger.error(f"Error in intent parsing: {e}", exc_info=True)
            return {
                "actions": [],
                "summary": "An error occurred while processing your command. Please try again."
            }
    
    async def _generate_response(self, prompt: str) -> str:
        """Generate response from Gemini API."""
        try:
            response = self.model.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            bot_logger.error(f"Gemini API error: {e}", exc_info=True)
            raise
    
    async def analyze_server(self, guild_data: Dict) -> Dict[str, Any]:
        """Analyze server structure and provide recommendations."""
        analysis_prompt = f"""
Analyze this Discord server structure and provide improvement suggestions.

SERVER DATA:
{json.dumps(guild_data, indent=2)}

Provide a JSON response with:
{{
  "actions": [
    {{
      "type": "analyze_server",
      "analysis": "detailed analysis of current structure",
      "missing_elements": ["list", "of", "missing", "elements"],
      "recommendations": ["list", "of", "specific", "recommendations"],
      "reason": "Server structure analysis"
    }}
  ],
  "summary": "Brief summary of analysis and key recommendations"
}}

Focus on:
1. Missing essential channels (welcome, rules, announcements)
2. Poor organization (channels not in categories)
3. Permission issues
4. Missing roles for moderation
5. Voice channel availability
6. Community engagement features
"""
        
        try:
            response = await self._generate_response(analysis_prompt)
            return json.loads(response)
        except Exception as e:
            bot_logger.error(f"Error in server analysis: {e}", exc_info=True)
            return {
                "actions": [{
                    "type": "analyze_server",
                    "analysis": "Unable to complete analysis due to an error.",
                    "missing_elements": [],
                    "recommendations": ["Please try the analysis again."],
                    "reason": "Analysis error"
                }],
                "summary": "Server analysis encountered an error."
            }

# Global AI service instance
ai_service = AIService()
