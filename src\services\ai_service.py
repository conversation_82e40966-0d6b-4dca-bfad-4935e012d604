"""
AI service for natural language processing using Groq API.
"""
import json
from groq import <PERSON>roq
from typing import Dict, List, Any, Optional
import config
from src.utils.logger import bot_logger

class AIService:
    """Service for processing natural language commands using Groq AI."""
    
    def __init__(self, model_name: str = 'meta-llama/llama-4-maverick-17b-128e-instruct'):
        # Validate API key
        if not config.GROQ_API_KEY or config.GROQ_API_KEY == "your_groq_api_key_here":
            bot_logger.error("Invalid or missing Groq API key in config")
            raise ValueError("Groq API key not configured properly")

        # Configure Groq client
        self.client = Groq(api_key=config.GROQ_API_KEY)
        self.model_name = model_name
        
        # Model configurations optimized for Discord bot usage
        self.model_configs = {
            'meta-llama/llama-4-maverick-17b-128e-instruct': {
                'description': 'OPTIMAL for Discord bots - fastest, most reliable (0.19s avg)',
                'context_window': 131072,
                'best_for': 'Real-time Discord responses, JSON generation',
                'score': 97.5
            },
            'llama3-8b-8192': {
                'description': 'Fast and efficient alternative (0.22s avg)',
                'context_window': 8192,
                'best_for': 'Quick responses, basic commands',
                'score': 97.3
            },
            'llama-3.1-8b-instant': {
                'description': 'Instant responses with good quality (0.21s avg)',
                'context_window': 131072,
                'best_for': 'Ultra-fast responses',
                'score': 97.1
            },
            'llama-3.3-70b-versatile': {
                'description': 'High quality for complex tasks (0.23s avg)',
                'context_window': 131072,
                'best_for': 'Complex server analysis, detailed responses',
                'score': 96.9
            }
        }
        
        # Test API connection
        self._test_api_connection()

        bot_logger.info(f"AI Service initialized with Groq model: {model_name}")
        bot_logger.info(f"Model purpose: {self.model_configs.get(model_name, {}).get('best_for', 'Unknown')}")
        
        # Enhanced system prompt for multi-intent parsing
        self.system_prompt = """
You are an advanced AI assistant that converts complex, multi-step natural language Discord server management commands into structured JSON actions.

Your task is to analyze user input and generate a JSON response with these parts:
1. "intents": Array of detected intents with dependencies
2. "actions": Array of specific actions to execute in proper order
3. "summary": Brief user-friendly description of what will be done
4. "execution_plan": Step-by-step execution strategy
5. "dependencies": Action dependencies and prerequisites

MULTI-INTENT PARSING CAPABILITIES:
- Parse numbered/bulleted lists: "1. Create category 2. Add channels 3. Set permissions"
- Handle nested instructions: "Organize into: Company (with #general, #announcements), Support (with #help)"
- Process abstract concepts: "clean up the mess", "organize better", "merge similar channels"
- Recognize synonyms: channel/room, category/section, role/rank, permission/access
- Support conditional logic: "if no admin category exists, create one"
- Chain dependent actions: create category THEN move channels THEN set permissions

AVAILABLE ACTION TYPES:

BASIC ACTIONS:
- create_channel: Create text/voice channels
- create_category: Create channel categories
- delete_channel: Delete channels
- delete_category: Delete categories
- create_role: Create roles
- delete_role: Delete roles
- modify_permissions: Change channel/role permissions
- analyze_server: Analyze current server structure

ADVANCED CHANNEL ACTIONS:
- create_channel_template: Create multiple channels from templates (gaming_community, professional_workspace, study_group)
- organize_channels: Automatically organize uncategorized channels into logical categories
- duplicate_channel: Copy a channel with all its settings
- bulk_delete_channels: Delete multiple channels matching a pattern
- edit_channel_permissions: Modify permissions for existing channels with granular control
- edit_channel: Modify channel properties (name, topic, slowmode, NSFW, category)

ADVANCED ROLE ACTIONS:
- create_role_hierarchy: Create complete role hierarchies (gaming_community, professional_workspace, study_group)
- assign_role_colors: Apply color schemes to roles (rainbow, professional, warm)
- cleanup_roles: Remove unused roles with no members
- organize_role_hierarchy: Organize roles by permission level
- bulk_assign_role: Assign roles to members based on criteria (no_roles, new_members)

EMOJI AND REACTION ACTIONS:
- add_custom_emoji: Upload custom emojis to the server
- manage_emoji: Rename, delete, or organize custom emojis
- add_reaction: Add reactions to messages
- remove_reaction: Remove reactions from messages

STRUCTURAL REORGANIZATION ACTIONS:
- reorganize_server_structure: Comprehensive server restructuring based on analysis
- merge_channels: Combine similar channels with content preservation
- auto_categorize: Intelligent channel grouping based on names/topics
- suggest_organization: Propose structure improvements without executing
- analyze_redundancy: Detect duplicate or similar channels/roles
- optimize_structure: Streamline server organization for better usability

TEMPLATE ACTIONS:
- apply_server_template: Apply complete server templates (gaming_community, professional_workspace, educational)

ENHANCED RESPONSE SCHEMA:
{
  "intents": [
    {
      "intent_id": "intent_1",
      "description": "Create support category",
      "priority": 1,
      "dependencies": []
    },
    {
      "intent_id": "intent_2",
      "description": "Add help channels",
      "priority": 2,
      "dependencies": ["intent_1"]
    }
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "action_type",
      "name": "item_name",
      "channel_type": "text|voice|stage", // for create_channel
      "category": "category_name", // optional, for channel placement
      "permissions": {
        "role_name": ["permission1", "permission2"],
        "@everyone": ["view_channel"]
      },
      "permission_operation": "grant|deny|remove|reset", // for edit_channel_permissions
      "permission_preset": "read-only|mute-members|admin-only|public|restricted", // optional preset
      "properties": { // for edit_channel
        "name": "new_name",
        "topic": "new topic",
        "slowmode_delay": 30,
        "nsfw": false
      },
      "merge_targets": ["channel1", "channel2"], // for merge_channels
      "organization_strategy": "by_purpose|by_activity|by_permissions", // for reorganize actions
      "reason": "explanation for this action"
    }
  ],
  "summary": "Brief description of what will be done",
  "execution_plan": {
    "total_steps": 3,
    "estimated_time": "30 seconds",
    "rollback_strategy": "delete_created_items",
    "risk_level": "low|medium|high"
  },
  "dependencies": {
    "action_1": [],
    "action_2": ["action_1"],
    "action_3": ["action_1", "action_2"]
  }
}

PERMISSION TYPES:
- view_channel, send_messages, read_message_history, add_reactions
- manage_messages, manage_channels, manage_roles, manage_webhooks
- connect, speak, mute_members, deafen_members, move_members (voice channels)
- use_slash_commands, embed_links, attach_files, mention_everyone

PERMISSION PRESETS:
- "read-only": view_channel=True, send_messages=False, add_reactions=True
- "mute-members": view_channel=True, send_messages=False, add_reactions=False
- "admin-only": view_channel=False (for @everyone), all permissions for admins
- "public": view_channel=True, send_messages=True, add_reactions=True
- "restricted": view_channel=True, send_messages=False, add_reactions=False

ENHANCED PARSING RULES:
1. MULTI-INTENT DETECTION: Identify all separate intents in complex requests
2. DEPENDENCY ANALYSIS: Determine which actions must happen before others
3. SYNONYM RECOGNITION: channel=room, category=section, role=rank, permission=access
4. ABSTRACT INTERPRETATION: "clean up" = analyze + reorganize, "organize better" = auto_categorize
5. CONDITIONAL LOGIC: Handle "if X exists" or "unless Y" statements
6. LIST PROCESSING: Parse numbered/bulleted lists as sequential actions
7. NESTED STRUCTURES: Handle hierarchical instructions with sub-components
8. CONTEXT AWARENESS: Use server analysis results for intelligent decisions
9. ROLLBACK PLANNING: Always include rollback strategy for complex operations
10. PROGRESS TRACKING: Assign action_ids for execution monitoring
11. RESPOND WITH VALID JSON ONLY - NO EXTRA TEXT

SYNONYM MAPPING:
- channel/room/chat → create_channel
- category/section/group → create_category
- role/rank/position → create_role
- permission/access/rights → modify_permissions
- organize/structure/arrange → auto_categorize or reorganize_server_structure
- clean/cleanup/tidy → analyze_redundancy + optimize_structure
- merge/combine/join → merge_channels
- split/separate/divide → create multiple channels from one

MULTI-INTENT EXAMPLES:

Input: "Create a support system with: 1) Support category 2) #help-desk and #bug-reports channels 3) @Support role with manage messages permission"
Output: {
  "intents": [
    {"intent_id": "intent_1", "description": "Create Support category", "priority": 1, "dependencies": []},
    {"intent_id": "intent_2", "description": "Create help channels", "priority": 2, "dependencies": ["intent_1"]},
    {"intent_id": "intent_3", "description": "Create Support role with permissions", "priority": 3, "dependencies": []}
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "create_category",
      "name": "Support",
      "reason": "Create category for support system"
    },
    {
      "action_id": "action_2",
      "intent_id": "intent_2",
      "type": "create_channel",
      "name": "help-desk",
      "channel_type": "text",
      "category": "Support",
      "reason": "Create help desk channel"
    },
    {
      "action_id": "action_3",
      "intent_id": "intent_2",
      "type": "create_channel",
      "name": "bug-reports",
      "channel_type": "text",
      "category": "Support",
      "reason": "Create bug reports channel"
    },
    {
      "action_id": "action_4",
      "intent_id": "intent_3",
      "type": "create_role",
      "name": "Support",
      "permissions": ["manage_messages"],
      "reason": "Create Support role with message management"
    }
  ],
  "summary": "Creating complete support system with category, channels, and role",
  "execution_plan": {
    "total_steps": 4,
    "estimated_time": "45 seconds",
    "rollback_strategy": "delete_created_items",
    "risk_level": "low"
  },
  "dependencies": {
    "action_1": [],
    "action_2": ["action_1"],
    "action_3": ["action_1"],
    "action_4": []
  }
}

Input: "My server is disorganized. Split everything into Company (for business), Community (for members), and Staff (admin-only)"
Output: {
  "intents": [
    {"intent_id": "intent_1", "description": "Analyze current server structure", "priority": 1, "dependencies": []},
    {"intent_id": "intent_2", "description": "Create new organizational structure", "priority": 2, "dependencies": ["intent_1"]},
    {"intent_id": "intent_3", "description": "Reorganize existing channels", "priority": 3, "dependencies": ["intent_2"]}
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "analyze_server",
      "analysis_type": "comprehensive",
      "reason": "Analyze current structure before reorganization"
    },
    {
      "action_id": "action_2",
      "intent_id": "intent_2",
      "type": "reorganize_server_structure",
      "organization_strategy": "by_purpose",
      "target_structure": {
        "Company": {"purpose": "business", "access": "public"},
        "Community": {"purpose": "members", "access": "public"},
        "Staff": {"purpose": "admin", "access": "admin-only"}
      },
      "reason": "Create new three-tier organizational structure"
    }
  ],
  "summary": "Analyzing and reorganizing server into Company, Community, and Staff sections",
  "execution_plan": {
    "total_steps": 2,
    "estimated_time": "2 minutes",
    "rollback_strategy": "restore_original_structure",
    "risk_level": "medium"
  },
  "dependencies": {
    "action_1": [],
    "action_2": ["action_1"]
  }
}
}

Input: "Merge #help and #support into one channel, then create a proper support category"
Output: {
  "intents": [
    {"intent_id": "intent_1", "description": "Merge help channels", "priority": 1, "dependencies": []},
    {"intent_id": "intent_2", "description": "Create support category", "priority": 2, "dependencies": ["intent_1"]}
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "merge_channels",
      "merge_targets": ["help", "support"],
      "new_name": "help-support",
      "reason": "Merge duplicate help channels into single channel"
    },
    {
      "action_id": "action_2",
      "intent_id": "intent_2",
      "type": "create_category",
      "name": "Support",
      "reason": "Create proper support category"
    },
    {
      "action_id": "action_3",
      "intent_id": "intent_2",
      "type": "edit_channel",
      "name": "help-support",
      "properties": {"category": "Support"},
      "reason": "Move merged channel to support category"
    }
  ],
  "summary": "Merging help channels and organizing into proper support category",
  "execution_plan": {
    "total_steps": 3,
    "estimated_time": "1 minute",
    "rollback_strategy": "restore_original_channels",
    "risk_level": "medium"
  },
  "dependencies": {
    "action_1": [],
    "action_2": [],
    "action_3": ["action_1", "action_2"]
  }
}

Input: "Design a server for my gaming community"
Output: {
  "actions": [
    {
      "type": "apply_server_template",
      "template_name": "gaming_community",
      "reason": "Apply complete gaming community template with roles, channels, and permissions"
    }
  ],
  "summary": "Applying complete gaming community template with organized roles, channels, and permissions"
}

Input: "Set up a professional workspace"
Output: {
  "actions": [
    {
      "type": "apply_server_template",
      "template_name": "professional_workspace",
      "reason": "Apply professional workspace template for business use"
    }
  ],
  "summary": "Creating professional workspace with business-appropriate structure and roles"
}

Input: "Organize my messy server"
Output: {
  "actions": [
    {
      "type": "organize_channels",
      "reason": "Organize uncategorized channels into logical categories"
    },
    {
      "type": "organize_role_hierarchy",
      "reason": "Organize roles by permission level"
    },
    {
      "type": "cleanup_roles",
      "reason": "Remove unused roles with no members"
    }
  ],
  "summary": "Organizing server structure by categorizing channels and cleaning up roles"
}

Input: "Make #announcements read-only for @everyone but allow @Moderators to post"
Output: {
  "actions": [
    {
      "type": "edit_channel_permissions",
      "name": "announcements",
      "permission_operation": "grant",
      "permissions": {
        "@everyone": ["view_channel", "add_reactions"],
        "Moderator": ["view_channel", "send_messages", "add_reactions", "manage_messages"]
      },
      "reason": "Set announcements channel to read-only for everyone except moderators"
    }
  ],
  "summary": "Making #announcements read-only for @everyone while allowing Moderators to post"
}

Input: "Change #general topic to 'Welcome! Please read #rules first' and enable 30-second slowmode"
Output: {
  "actions": [
    {
      "type": "edit_channel",
      "name": "general",
      "properties": {
        "topic": "Welcome! Please read #rules first",
        "slowmode_delay": 30
      },
      "reason": "Update general channel topic and enable slowmode"
    }
  ],
  "summary": "Updating #general topic and enabling 30-second slowmode"
}

Input: "Add our custom logo as an emoji called 'serverlogo'"
Output: {
  "actions": [
    {
      "type": "add_custom_emoji",
      "name": "serverlogo",
      "reason": "Add custom server logo emoji"
    }
  ],
  "summary": "Adding custom emoji 'serverlogo' to the server"
}

Now process the user's command and respond with valid JSON only.
"""
    
    async def parse_intent(self, user_input: str, server_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Parse complex, multi-step natural language input into structured actions with dependencies.

        Args:
            user_input: The user's natural language command (can be multi-step)
            server_context: Optional context about current server state

        Returns:
            Dictionary with enhanced structure:
            - 'intents': Array of detected intents with dependencies
            - 'actions': Array of specific actions to execute in proper order
            - 'summary': Brief user-friendly description
            - 'execution_plan': Step-by-step execution strategy
            - 'dependencies': Action dependencies and prerequisites
        """
        try:
            # Prepare the prompt with context
            context_info = ""
            if server_context:
                context_info = f"\nCURRENT SERVER CONTEXT:\n{json.dumps(server_context, indent=2)}\n"
            
            full_prompt = f"{self.system_prompt}{context_info}\nUSER COMMAND: {user_input}"
            
            bot_logger.debug(f"Sending prompt to Groq: {user_input}")
            
            # Generate response using Groq
            response = await self._generate_response(full_prompt)
            
            # Parse JSON response
            try:
                parsed_response = json.loads(response)
                
                # Validate response structure
                if not isinstance(parsed_response, dict):
                    raise ValueError("Response is not a dictionary")
                
                if 'actions' not in parsed_response:
                    raise ValueError("Response missing 'actions' key")
                
                # Validate and enhance response structure
                enhanced_response = self._validate_and_enhance_response(parsed_response)

                bot_logger.info(f"Successfully parsed multi-intent: {len(enhanced_response.get('actions', []))} actions, {len(enhanced_response.get('intents', []))} intents")
                return enhanced_response
                
            except json.JSONDecodeError as e:
                bot_logger.error(f"Failed to parse JSON response: {e}")
                bot_logger.debug(f"Raw response: {response}")

                # Use local fallback when AI parsing fails
                bot_logger.info("Using local fallback command processing")
                return self._get_local_fallback_response(user_input)

        except Exception as e:
            bot_logger.error(f"Error in intent parsing: {e}", exc_info=True)
            # Use local fallback when AI service fails completely
            bot_logger.info("Using local fallback due to AI service error")
            return self._get_local_fallback_response(user_input)
    
    async def _generate_response(self, prompt: str) -> str:
        """Generate response from Groq API with enhanced error handling."""
        try:
            bot_logger.debug(f"Sending request to Groq model: {self.model_name}")
            bot_logger.debug(f"Prompt length: {len(prompt)} characters")

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.1,  # Low temperature for consistent JSON output
                top_p=0.9
            )

            if not response or not response.choices:
                bot_logger.error("Groq API returned empty response object")
                return self._get_fallback_response()

            content = response.choices[0].message.content
            if not content or content.strip() == "":
                bot_logger.error("Groq API returned empty content")
                return self._get_fallback_response()

            bot_logger.debug(f"Received response length: {len(content)} characters")
            return content.strip()

        except Exception as e:
            bot_logger.error(f"Groq API error: {e}", exc_info=True)
            return self._get_fallback_response()

    def _get_fallback_response(self) -> str:
        """Generate a fallback JSON response when AI service fails."""
        return """{
            "actions": [{
                "type": "send_message",
                "content": "I'm experiencing technical difficulties with my AI service. Please try a simpler command or contact an administrator.",
                "reason": "AI service unavailable"
            }],
            "summary": "AI service temporarily unavailable",
            "intents": [{
                "intent_id": "fallback_1",
                "description": "Fallback response due to AI service failure",
                "priority": 1,
                "dependencies": []
            }],
            "execution_plan": {
                "total_steps": 1,
                "estimated_time": "immediate",
                "risk_level": "none"
            }
        }"""

    def _get_local_fallback_response(self, user_input: str) -> Dict[str, Any]:
        """Generate local fallback responses for common commands when AI fails."""
        user_input_lower = user_input.lower()

        # Simple pattern matching for common commands
        if any(word in user_input_lower for word in ['create', 'make', 'add']) and 'channel' in user_input_lower:
            return {
                "actions": [{
                    "type": "create_text_channel",
                    "name": "new-channel",
                    "reason": "Local fallback for channel creation"
                }],
                "summary": "Creating a new text channel (AI service unavailable)",
                "intents": [{"intent_id": "local_1", "description": "Create channel", "priority": 1}]
            }

        elif any(word in user_input_lower for word in ['analyze', 'check', 'health']) and 'server' in user_input_lower:
            return {
                "actions": [{
                    "type": "analyze_server",
                    "reason": "Local fallback for server analysis"
                }],
                "summary": "Analyzing server structure (AI service unavailable)",
                "intents": [{"intent_id": "local_2", "description": "Analyze server", "priority": 1}]
            }

        elif any(word in user_input_lower for word in ['help', 'commands', 'what']):
            return {
                "actions": [{
                    "type": "send_message",
                    "content": "**Available Commands (AI service unavailable):**\n• Create channels: 'create a channel'\n• Analyze server: 'analyze my server'\n• Get help: 'help'\n\nThe AI service is temporarily unavailable. Please try again later or use simpler commands.",
                    "reason": "Local help response"
                }],
                "summary": "Showing available commands",
                "intents": [{"intent_id": "local_3", "description": "Show help", "priority": 1}]
            }

        # Default fallback
        return {
            "actions": [{
                "type": "send_message",
                "content": "I'm currently experiencing technical difficulties. Please try:\n• Simpler commands\n• Rephrasing your request\n• Contacting an administrator\n\nExample: 'create a channel' or 'analyze my server'",
                "reason": "AI service unavailable"
            }],
            "summary": "AI service temporarily unavailable",
            "intents": [{"intent_id": "fallback", "description": "Service unavailable", "priority": 1}]
        }

    def _validate_and_enhance_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and enhance the AI response structure for multi-intent support."""
        enhanced = response.copy()

        # Ensure required fields exist
        if 'actions' not in enhanced:
            enhanced['actions'] = []
        if 'summary' not in enhanced:
            enhanced['summary'] = "Processing your request..."

        # Add missing multi-intent fields if not present
        if 'intents' not in enhanced:
            # Generate intents from actions if not provided
            enhanced['intents'] = self._generate_intents_from_actions(enhanced['actions'])

        if 'execution_plan' not in enhanced:
            enhanced['execution_plan'] = {
                "total_steps": len(enhanced['actions']),
                "estimated_time": f"{len(enhanced['actions']) * 15} seconds",
                "rollback_strategy": "delete_created_items",
                "risk_level": "low" if len(enhanced['actions']) <= 3 else "medium"
            }

        if 'dependencies' not in enhanced:
            enhanced['dependencies'] = self._generate_dependencies(enhanced['actions'])

        # Validate actions structure
        if not isinstance(enhanced['actions'], list):
            enhanced['actions'] = []

        # Add action_ids if missing
        for i, action in enumerate(enhanced['actions']):
            if 'action_id' not in action:
                action['action_id'] = f"action_{i+1}"

        return enhanced

    def _generate_intents_from_actions(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate intent structure from actions if not provided by AI."""
        intents = []
        intent_map = {}

        for i, action in enumerate(actions):
            action_type = action.get('type', 'unknown')

            # Group similar actions into intents
            intent_key = self._get_intent_category(action_type)

            if intent_key not in intent_map:
                intent_id = f"intent_{len(intents) + 1}"
                intent_map[intent_key] = intent_id
                intents.append({
                    "intent_id": intent_id,
                    "description": self._get_intent_description(intent_key),
                    "priority": len(intents) + 1,
                    "dependencies": []
                })

            # Link action to intent
            action['intent_id'] = intent_map[intent_key]

        return intents

    def _get_intent_category(self, action_type: str) -> str:
        """Categorize action types into intent groups."""
        category_map = {
            'create_channel': 'channel_creation',
            'create_category': 'category_creation',
            'create_role': 'role_creation',
            'edit_channel': 'channel_modification',
            'edit_channel_permissions': 'permission_management',
            'analyze_server': 'server_analysis',
            'reorganize_server_structure': 'server_reorganization',
            'merge_channels': 'channel_optimization',
            'auto_categorize': 'server_organization'
        }
        return category_map.get(action_type, 'general_action')

    def _get_intent_description(self, intent_category: str) -> str:
        """Get human-readable description for intent category."""
        descriptions = {
            'channel_creation': 'Create new channels',
            'category_creation': 'Create channel categories',
            'role_creation': 'Create server roles',
            'channel_modification': 'Modify channel properties',
            'permission_management': 'Manage permissions',
            'server_analysis': 'Analyze server structure',
            'server_reorganization': 'Reorganize server structure',
            'channel_optimization': 'Optimize channel organization',
            'server_organization': 'Organize server layout',
            'general_action': 'Perform server action'
        }
        return descriptions.get(intent_category, 'Perform server action')

    def _generate_dependencies(self, actions: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Generate action dependencies based on action types."""
        dependencies = {}

        for action in actions:
            action_id = action.get('action_id', 'unknown')
            action_type = action.get('type', 'unknown')
            dependencies[action_id] = []

            # Add dependencies based on action type logic
            if action_type in ['create_channel'] and action.get('category'):
                # Channel creation depends on category creation
                for other_action in actions:
                    if (other_action.get('type') == 'create_category' and
                        other_action.get('name') == action.get('category')):
                        dependencies[action_id].append(other_action.get('action_id', ''))

            elif action_type == 'reorganize_server_structure':
                # Reorganization depends on analysis
                for other_action in actions:
                    if other_action.get('type') == 'analyze_server':
                        dependencies[action_id].append(other_action.get('action_id', ''))

        return dependencies
    
    async def analyze_server(self, guild_data: Dict) -> Dict[str, Any]:
        """Analyze server structure and provide recommendations."""
        analysis_prompt = f"""
Analyze this Discord server structure and provide improvement suggestions.

SERVER DATA:
{json.dumps(guild_data, indent=2)}

Provide a JSON response with:
{{
  "actions": [
    {{
      "type": "analyze_server",
      "analysis": "detailed analysis of current structure",
      "missing_elements": ["list", "of", "missing", "elements"],
      "recommendations": ["list", "of", "specific", "recommendations"],
      "reason": "Server structure analysis"
    }}
  ],
  "summary": "Brief summary of analysis and key recommendations"
}}

Focus on:
1. Missing essential channels (welcome, rules, announcements)
2. Poor organization (channels not in categories)
3. Permission issues
4. Missing roles for moderation
5. Voice channel availability
6. Community engagement features

RESPOND WITH VALID JSON ONLY.
"""
        
        try:
            response = await self._generate_response(analysis_prompt)
            return json.loads(response)
        except Exception as e:
            bot_logger.error(f"Error in server analysis: {e}", exc_info=True)
            return {
                "actions": [{
                    "type": "analyze_server",
                    "analysis": "Unable to complete analysis due to an error.",
                    "missing_elements": [],
                    "recommendations": ["Please try the analysis again."],
                    "reason": "Analysis error"
                }],
                "summary": "Server analysis encountered an error."
            }

    def _test_api_connection(self):
        """Test Groq API connection with a simple request."""
        try:
            bot_logger.info("Testing Groq API connection...")

            # Simple test prompt
            test_response = self.client.chat.completions.create(
                messages=[
                    {"role": "user", "content": "Test connection. Respond with just 'OK'."}
                ],
                model=self.model_name,
                max_tokens=10,
                temperature=0
            )

            if test_response and test_response.choices:
                response_text = test_response.choices[0].message.content.strip()
                bot_logger.info(f"API connection test successful: {response_text}")
            else:
                bot_logger.warning("API connection test returned empty response")
                self._try_fallback_model()

        except Exception as e:
            bot_logger.error(f"API connection test failed: {e}")
            self._try_fallback_model()

    def _try_fallback_model(self):
        """Try switching to a more reliable fallback model."""
        fallback_models = [
            'llama-3.3-70b-versatile',
            'llama-3.1-70b-versatile',
            'mixtral-8x7b-32768'
        ]

        for fallback_model in fallback_models:
            if fallback_model != self.model_name:
                bot_logger.info(f"Trying fallback model: {fallback_model}")
                self.model_name = fallback_model
                try:
                    # Test the fallback model
                    test_response = self.client.chat.completions.create(
                        messages=[
                            {"role": "user", "content": "Test. Respond 'OK'."}
                        ],
                        model=self.model_name,
                        max_tokens=5,
                        temperature=0
                    )

                    if test_response and test_response.choices:
                        bot_logger.info(f"Successfully switched to fallback model: {fallback_model}")
                        return

                except Exception as e:
                    bot_logger.warning(f"Fallback model {fallback_model} also failed: {e}")
                    continue

        bot_logger.error("All fallback models failed. Bot will use local fallback responses.")

# Global AI service instance
ai_service = AIService()
