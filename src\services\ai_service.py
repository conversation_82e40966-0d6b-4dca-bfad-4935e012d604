"""
AI service for natural language processing using Groq API.
"""
import json
from groq import <PERSON>roq
from typing import Dict, List, Any, Optional
import config
from src.utils.logger import bot_logger

class AIService:
    """Service for processing natural language commands using Groq AI."""
    
    def __init__(self, model_name: str = 'meta-llama/llama-4-maverick-17b-128e-instruct'):
        # Validate API key
        if not config.GROQ_API_KEY or config.GROQ_API_KEY == "your_groq_api_key_here":
            bot_logger.error("Invalid or missing Groq API key in config")
            raise ValueError("Groq API key not configured properly")

        # Configure Groq client
        self.client = Groq(api_key=config.GROQ_API_KEY)
        self.model_name = model_name
        
        # Model configurations optimized for Discord bot usage
        self.model_configs = {
            'meta-llama/llama-4-maverick-17b-128e-instruct': {
                'description': 'OPTIMAL for Discord bots - fastest, most reliable (0.19s avg)',
                'context_window': 131072,
                'best_for': 'Real-time Discord responses, JSON generation',
                'score': 97.5
            },
            'llama3-8b-8192': {
                'description': 'Fast and efficient alternative (0.22s avg)',
                'context_window': 8192,
                'best_for': 'Quick responses, basic commands',
                'score': 97.3
            },
            'llama-3.1-8b-instant': {
                'description': 'Instant responses with good quality (0.21s avg)',
                'context_window': 131072,
                'best_for': 'Ultra-fast responses',
                'score': 97.1
            },
            'llama-3.3-70b-versatile': {
                'description': 'High quality for complex tasks (0.23s avg)',
                'context_window': 131072,
                'best_for': 'Complex server analysis, detailed responses',
                'score': 96.9
            }
        }
        
        # Test API connection
        self._test_api_connection()

        bot_logger.info(f"AI Service initialized with Groq model: {model_name}")
        bot_logger.info(f"Model purpose: {self.model_configs.get(model_name, {}).get('best_for', 'Unknown')}")
        
        # Classification-specific system prompts
        self.classification_prompt = """
You are a Discord server management request classifier. Analyze the user's request and classify it into exactly one of three categories:

SIMPLE: Single-action requests with clear, direct intent
- Examples: "create a channel", "delete #old-chat", "add @Member role", "set slowmode to 5 seconds"
- Characteristics: One clear action, specific target, minimal context needed

COMPLEX: Multi-step requests with explicit parameters and dependencies
- Examples: "create a support system with #help-desk, #bug-reports channels and @Support role with manage messages permission"
- Characteristics: Multiple related actions, explicit dependencies, structured requirements

CREATIVE: Open-ended requests requiring server analysis and intelligent design
- Examples: "design a discord server for a minecraft SMP", "reorganize my server to be more professional", "suggest improvements"
- Characteristics: Requires creativity, server analysis, design thinking, recommendations

Respond with ONLY a JSON object:
{
  "classification": "SIMPLE|COMPLEX|CREATIVE",
  "confidence_score": 0.0-1.0,
  "reasoning": "Brief explanation of classification decision"
}
"""

        # Enhanced system prompt for multi-intent parsing
        self.system_prompt = """
You are an advanced AI assistant that converts complex, multi-step natural language Discord server management commands into structured JSON actions.

Your task is to analyze user input and generate a JSON response with these parts:
1. "intents": Array of detected intents with dependencies
2. "actions": Array of specific actions to execute in proper order
3. "summary": Brief user-friendly description of what will be done
4. "execution_plan": Step-by-step execution strategy
5. "dependencies": Action dependencies and prerequisites

MULTI-INTENT PARSING CAPABILITIES:
- Parse numbered/bulleted lists: "1. Create category 2. Add channels 3. Set permissions"
- Handle nested instructions: "Organize into: Company (with #general, #announcements), Support (with #help)"
- Process abstract concepts: "clean up the mess", "organize better", "merge similar channels"
- Recognize synonyms: channel/room, category/section, role/rank, permission/access
- Support conditional logic: "if no admin category exists, create one"
- Chain dependent actions: create category THEN move channels THEN set permissions

AVAILABLE ACTION TYPES:

BASIC ACTIONS:
- create_channel: Create text/voice channels
- create_category: Create channel categories
- delete_channel: Delete channels
- delete_category: Delete categories
- create_role: Create roles
- delete_role: Delete roles
- modify_permissions: Change channel/role permissions
- analyze_server: Analyze current server structure

ADVANCED CHANNEL ACTIONS:
- create_channel_template: Create multiple channels from templates (gaming_community, professional_workspace, study_group)
- organize_channels: Automatically organize uncategorized channels into logical categories
- duplicate_channel: Copy a channel with all its settings
- bulk_delete_channels: Delete multiple channels matching a pattern
- edit_channel_permissions: Modify permissions for existing channels with granular control
- edit_channel: Modify channel properties (name, topic, slowmode, NSFW, category)

ADVANCED ROLE ACTIONS:
- create_role_hierarchy: Create complete role hierarchies (gaming_community, professional_workspace, study_group)
- assign_role_colors: Apply color schemes to roles (rainbow, professional, warm)
- cleanup_roles: Remove unused roles with no members
- organize_role_hierarchy: Organize roles by permission level
- bulk_assign_role: Assign roles to members based on criteria (no_roles, new_members)

EMOJI AND REACTION ACTIONS:
- add_custom_emoji: Upload custom emojis to the server
- manage_emoji: Rename, delete, or organize custom emojis
- add_reaction: Add reactions to messages
- remove_reaction: Remove reactions from messages

STRUCTURAL REORGANIZATION ACTIONS:
- reorganize_server_structure: Comprehensive server restructuring based on analysis
- merge_channels: Combine similar channels with content preservation
- auto_categorize: Intelligent channel grouping based on names/topics
- suggest_organization: Propose structure improvements without executing
- analyze_redundancy: Detect duplicate or similar channels/roles
- optimize_structure: Streamline server organization for better usability

TEMPLATE ACTIONS:
- apply_server_template: Apply complete server templates (gaming_community, professional_workspace, educational)

ENHANCED RESPONSE SCHEMA:
{
  "intents": [
    {
      "intent_id": "intent_1",
      "description": "Create support category",
      "priority": 1,
      "dependencies": []
    },
    {
      "intent_id": "intent_2",
      "description": "Add help channels",
      "priority": 2,
      "dependencies": ["intent_1"]
    }
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "action_type",
      "name": "item_name",
      "channel_type": "text|voice|stage", // for create_channel
      "category": "category_name", // optional, for channel placement
      "permissions": {
        "role_name": ["permission1", "permission2"],
        "@everyone": ["view_channel"]
      },
      "permission_operation": "grant|deny|remove|reset", // for edit_channel_permissions
      "permission_preset": "read-only|mute-members|admin-only|public|restricted", // optional preset
      "properties": { // for edit_channel
        "name": "new_name",
        "topic": "new topic",
        "slowmode_delay": 30,
        "nsfw": false
      },
      "merge_targets": ["channel1", "channel2"], // for merge_channels
      "organization_strategy": "by_purpose|by_activity|by_permissions", // for reorganize actions
      "reason": "explanation for this action"
    }
  ],
  "summary": "Brief description of what will be done",
  "execution_plan": {
    "total_steps": 3,
    "estimated_time": "30 seconds",
    "rollback_strategy": "delete_created_items",
    "risk_level": "low|medium|high"
  },
  "dependencies": {
    "action_1": [],
    "action_2": ["action_1"],
    "action_3": ["action_1", "action_2"]
  }
}

PERMISSION TYPES:
- view_channel, send_messages, read_message_history, add_reactions
- manage_messages, manage_channels, manage_roles, manage_webhooks
- connect, speak, mute_members, deafen_members, move_members (voice channels)
- use_slash_commands, embed_links, attach_files, mention_everyone

PERMISSION PRESETS:
- "read-only": view_channel=True, send_messages=False, add_reactions=True
- "mute-members": view_channel=True, send_messages=False, add_reactions=False
- "admin-only": view_channel=False (for @everyone), all permissions for admins
- "public": view_channel=True, send_messages=True, add_reactions=True
- "restricted": view_channel=True, send_messages=False, add_reactions=False

ENHANCED PARSING RULES:
1. MULTI-INTENT DETECTION: Identify all separate intents in complex requests
2. DEPENDENCY ANALYSIS: Determine which actions must happen before others
3. SYNONYM RECOGNITION: channel=room, category=section, role=rank, permission=access
4. ABSTRACT INTERPRETATION: "clean up" = analyze + reorganize, "organize better" = auto_categorize
5. CONDITIONAL LOGIC: Handle "if X exists" or "unless Y" statements
6. LIST PROCESSING: Parse numbered/bulleted lists as sequential actions
7. NESTED STRUCTURES: Handle hierarchical instructions with sub-components
8. CONTEXT AWARENESS: Use server analysis results for intelligent decisions
9. ROLLBACK PLANNING: Always include rollback strategy for complex operations
10. PROGRESS TRACKING: Assign action_ids for execution monitoring
11. RESPOND WITH VALID JSON ONLY - NO EXTRA TEXT

SYNONYM MAPPING:
- channel/room/chat → create_channel
- category/section/group → create_category
- role/rank/position → create_role
- permission/access/rights → modify_permissions
- organize/structure/arrange → auto_categorize or reorganize_server_structure
- clean/cleanup/tidy → analyze_redundancy + optimize_structure
- merge/combine/join → merge_channels
- split/separate/divide → create multiple channels from one

MULTI-INTENT EXAMPLES:

Input: "Create a support system with: 1) Support category 2) #help-desk and #bug-reports channels 3) @Support role with manage messages permission"
Output: {
  "intents": [
    {"intent_id": "intent_1", "description": "Create Support category", "priority": 1, "dependencies": []},
    {"intent_id": "intent_2", "description": "Create help channels", "priority": 2, "dependencies": ["intent_1"]},
    {"intent_id": "intent_3", "description": "Create Support role with permissions", "priority": 3, "dependencies": []}
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "create_category",
      "name": "Support",
      "reason": "Create category for support system"
    },
    {
      "action_id": "action_2",
      "intent_id": "intent_2",
      "type": "create_channel",
      "name": "help-desk",
      "channel_type": "text",
      "category": "Support",
      "reason": "Create help desk channel"
    },
    {
      "action_id": "action_3",
      "intent_id": "intent_2",
      "type": "create_channel",
      "name": "bug-reports",
      "channel_type": "text",
      "category": "Support",
      "reason": "Create bug reports channel"
    },
    {
      "action_id": "action_4",
      "intent_id": "intent_3",
      "type": "create_role",
      "name": "Support",
      "permissions": ["manage_messages"],
      "reason": "Create Support role with message management"
    }
  ],
  "summary": "Creating complete support system with category, channels, and role",
  "execution_plan": {
    "total_steps": 4,
    "estimated_time": "45 seconds",
    "rollback_strategy": "delete_created_items",
    "risk_level": "low"
  },
  "dependencies": {
    "action_1": [],
    "action_2": ["action_1"],
    "action_3": ["action_1"],
    "action_4": []
  }
}

Input: "My server is disorganized. Split everything into Company (for business), Community (for members), and Staff (admin-only)"
Output: {
  "intents": [
    {"intent_id": "intent_1", "description": "Analyze current server structure", "priority": 1, "dependencies": []},
    {"intent_id": "intent_2", "description": "Create new organizational structure", "priority": 2, "dependencies": ["intent_1"]},
    {"intent_id": "intent_3", "description": "Reorganize existing channels", "priority": 3, "dependencies": ["intent_2"]}
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "analyze_server",
      "analysis_type": "comprehensive",
      "reason": "Analyze current structure before reorganization"
    },
    {
      "action_id": "action_2",
      "intent_id": "intent_2",
      "type": "reorganize_server_structure",
      "organization_strategy": "by_purpose",
      "target_structure": {
        "Company": {"purpose": "business", "access": "public"},
        "Community": {"purpose": "members", "access": "public"},
        "Staff": {"purpose": "admin", "access": "admin-only"}
      },
      "reason": "Create new three-tier organizational structure"
    }
  ],
  "summary": "Analyzing and reorganizing server into Company, Community, and Staff sections",
  "execution_plan": {
    "total_steps": 2,
    "estimated_time": "2 minutes",
    "rollback_strategy": "restore_original_structure",
    "risk_level": "medium"
  },
  "dependencies": {
    "action_1": [],
    "action_2": ["action_1"]
  }
}
}

Input: "Merge #help and #support into one channel, then create a proper support category"
Output: {
  "intents": [
    {"intent_id": "intent_1", "description": "Merge help channels", "priority": 1, "dependencies": []},
    {"intent_id": "intent_2", "description": "Create support category", "priority": 2, "dependencies": ["intent_1"]}
  ],
  "actions": [
    {
      "action_id": "action_1",
      "intent_id": "intent_1",
      "type": "merge_channels",
      "merge_targets": ["help", "support"],
      "new_name": "help-support",
      "reason": "Merge duplicate help channels into single channel"
    },
    {
      "action_id": "action_2",
      "intent_id": "intent_2",
      "type": "create_category",
      "name": "Support",
      "reason": "Create proper support category"
    },
    {
      "action_id": "action_3",
      "intent_id": "intent_2",
      "type": "edit_channel",
      "name": "help-support",
      "properties": {"category": "Support"},
      "reason": "Move merged channel to support category"
    }
  ],
  "summary": "Merging help channels and organizing into proper support category",
  "execution_plan": {
    "total_steps": 3,
    "estimated_time": "1 minute",
    "rollback_strategy": "restore_original_channels",
    "risk_level": "medium"
  },
  "dependencies": {
    "action_1": [],
    "action_2": [],
    "action_3": ["action_1", "action_2"]
  }
}

Input: "Design a server for my gaming community"
Output: {
  "actions": [
    {
      "type": "apply_server_template",
      "template_name": "gaming_community",
      "reason": "Apply complete gaming community template with roles, channels, and permissions"
    }
  ],
  "summary": "Applying complete gaming community template with organized roles, channels, and permissions"
}

Input: "Set up a professional workspace"
Output: {
  "actions": [
    {
      "type": "apply_server_template",
      "template_name": "professional_workspace",
      "reason": "Apply professional workspace template for business use"
    }
  ],
  "summary": "Creating professional workspace with business-appropriate structure and roles"
}

Input: "Organize my messy server"
Output: {
  "actions": [
    {
      "type": "organize_channels",
      "reason": "Organize uncategorized channels into logical categories"
    },
    {
      "type": "organize_role_hierarchy",
      "reason": "Organize roles by permission level"
    },
    {
      "type": "cleanup_roles",
      "reason": "Remove unused roles with no members"
    }
  ],
  "summary": "Organizing server structure by categorizing channels and cleaning up roles"
}

Input: "Make #announcements read-only for @everyone but allow @Moderators to post"
Output: {
  "actions": [
    {
      "type": "edit_channel_permissions",
      "name": "announcements",
      "permission_operation": "grant",
      "permissions": {
        "@everyone": ["view_channel", "add_reactions"],
        "Moderator": ["view_channel", "send_messages", "add_reactions", "manage_messages"]
      },
      "reason": "Set announcements channel to read-only for everyone except moderators"
    }
  ],
  "summary": "Making #announcements read-only for @everyone while allowing Moderators to post"
}

Input: "Change #general topic to 'Welcome! Please read #rules first' and enable 30-second slowmode"
Output: {
  "actions": [
    {
      "type": "edit_channel",
      "name": "general",
      "properties": {
        "topic": "Welcome! Please read #rules first",
        "slowmode_delay": 30
      },
      "reason": "Update general channel topic and enable slowmode"
    }
  ],
  "summary": "Updating #general topic and enabling 30-second slowmode"
}

Input: "Add our custom logo as an emoji called 'serverlogo'"
Output: {
  "actions": [
    {
      "type": "add_custom_emoji",
      "name": "serverlogo",
      "reason": "Add custom server logo emoji"
    }
  ],
  "summary": "Adding custom emoji 'serverlogo' to the server"
}

Now process the user's command and respond with valid JSON only.
"""
    
    async def parse_intent(self, user_input: str, server_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        THREE-STAGE AI PROCESSING PIPELINE:
        Stage 1: Request Classification (SIMPLE/COMPLEX/CREATIVE)
        Stage 2: Adaptive Intent Detection (classification-specific processing)
        Stage 3: Tailored Response Generation (depth appropriate to request type)

        Args:
            user_input: The user's natural language command
            server_context: Current server state for context-aware parsing

        Returns:
            Dict containing parsed intents, actions, execution plan, and classification metadata
        """
        pipeline_start_time = time.time()

        try:
            # Get server context if not provided
            if server_context is None:
                server_context = {}

            # STAGE 1: REQUEST CLASSIFICATION
            classification = await self.classify_request(user_input)
            classification_type = classification["classification"]
            confidence_score = classification["confidence_score"]

            bot_logger.info(f"🎯 STAGE 1: Classified as {classification_type} "
                          f"(confidence: {confidence_score:.2f})")

            # STAGE 2: ADAPTIVE INTENT DETECTION
            if classification_type == "SIMPLE" and confidence_score >= 0.8:
                bot_logger.info("🚀 STAGE 2: Processing with SIMPLE pipeline")
                result = await self.process_simple_request(user_input, server_context)

            elif classification_type == "CREATIVE" and confidence_score >= 0.7:
                bot_logger.info("🎨 STAGE 2: Processing with CREATIVE pipeline")
                result = await self.process_creative_request(user_input, server_context)

            else:
                # Use COMPLEX processing for medium confidence or COMPLEX classification
                bot_logger.info("⚙️ STAGE 2: Processing with COMPLEX pipeline")
                result = await self.process_complex_request(user_input, server_context)

            # STAGE 3: ADD CLASSIFICATION METADATA
            result["classification"] = classification_type
            result["confidence_score"] = confidence_score
            result["processing_strategy"] = self._get_processing_strategy(classification_type)

            # Ensure execution_plan exists with required fields
            if "execution_plan" not in result:
                result["execution_plan"] = {}

            execution_plan = result["execution_plan"]
            if "estimated_complexity" not in execution_plan:
                execution_plan["estimated_complexity"] = self._estimate_complexity(classification_type, result)
            if "requires_confirmation" not in execution_plan:
                execution_plan["requires_confirmation"] = self._requires_confirmation(result)

            # Log comprehensive performance metrics
            total_time = time.time() - pipeline_start_time
            intents_count = len(result.get('intents', []))
            actions_count = len(result.get('actions', []))

            bot_logger.log_ai_parsing(
                user_input, total_time, intents_count, actions_count, True
            )

            bot_logger.info(f"✅ STAGE 3: Pipeline complete in {total_time:.3f}s "
                          f"({intents_count} intents, {actions_count} actions)")

            return result

        except Exception as e:
            bot_logger.error(f"Error in three-stage pipeline: {e}", exc_info=True)
            # Fallback to local processing
            bot_logger.info("🔄 Pipeline failed, using local fallback")
            return self._get_local_fallback_response(user_input)

    async def process_complex_request(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Stage 2B: Process COMPLEX requests with current multi-intent parsing.
        Target: <2 seconds response time.
        """
        start_time = time.time()
        bot_logger.debug("Processing COMPLEX request with multi-intent parsing")

        # Use existing enhanced system prompt for complex processing
        prompt = f"{self.system_prompt}\n\nSERVER CONTEXT:\n{json.dumps(server_context, indent=2)}\n\nUSER COMMAND: {user_input}"

        try:
            response = await self._generate_response(prompt)
            result = json.loads(response)

            # Validate and enhance response
            result = self._validate_and_enhance_response(result)

            processing_time = time.time() - start_time
            bot_logger.info(f"COMPLEX request processed in {processing_time:.3f}s")

            return result

        except json.JSONDecodeError as e:
            bot_logger.error(f"Failed to parse complex request JSON: {e}")
            return self._get_local_fallback_response(user_input)
        except Exception as e:
            bot_logger.error(f"Error processing complex request: {e}")
            return self._get_local_fallback_response(user_input)

    def _get_processing_strategy(self, classification: str) -> str:
        """Get processing strategy name for classification."""
        strategy_map = {
            "SIMPLE": "direct",
            "COMPLEX": "multi_step",
            "CREATIVE": "creative_analysis"
        }
        return strategy_map.get(classification, "multi_step")

    def _estimate_complexity(self, classification: str, result: Dict[str, Any]) -> str:
        """Estimate complexity based on classification and actions."""
        if classification == "SIMPLE":
            return "low"
        elif classification == "CREATIVE":
            return "high"
        else:
            action_count = len(result.get('actions', []))
            if action_count <= 2:
                return "medium"
            else:
                return "high"

    def _requires_confirmation(self, result: Dict[str, Any]) -> bool:
        """Determine if actions require user confirmation."""
        actions = result.get('actions', [])

        # Actions that require confirmation
        confirmation_actions = ['delete_channel', 'delete_role', 'ban_member', 'kick_member']

        for action in actions:
            if action.get('type') in confirmation_actions:
                return True

        # Require confirmation for high-impact operations
        if len(actions) > 5:
            return True

        return False
    
    async def _generate_response(self, prompt: str) -> str:
        """Generate response from Groq API with enhanced error handling."""
        try:
            bot_logger.debug(f"Sending request to Groq model: {self.model_name}")
            bot_logger.debug(f"Prompt length: {len(prompt)} characters")

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.1,  # Low temperature for consistent JSON output
                top_p=0.9
            )

            if not response or not response.choices:
                bot_logger.error("Groq API returned empty response object")
                return self._get_fallback_response()

            content = response.choices[0].message.content
            if not content or content.strip() == "":
                bot_logger.error("Groq API returned empty content")
                return self._get_fallback_response()

            bot_logger.debug(f"Received response length: {len(content)} characters")
            return content.strip()

        except Exception as e:
            bot_logger.error(f"Groq API error: {e}", exc_info=True)
            return self._get_fallback_response()

    def _get_fallback_response(self) -> str:
        """Generate a fallback JSON response when AI service fails."""
        return """{
            "actions": [{
                "type": "send_message",
                "content": "I'm experiencing technical difficulties with my AI service. Please try a simpler command or contact an administrator.",
                "reason": "AI service unavailable"
            }],
            "summary": "AI service temporarily unavailable",
            "intents": [{
                "intent_id": "fallback_1",
                "description": "Fallback response due to AI service failure",
                "priority": 1,
                "dependencies": []
            }],
            "execution_plan": {
                "total_steps": 1,
                "estimated_time": "immediate",
                "risk_level": "none"
            }
        }"""

    def _get_local_fallback_response(self, user_input: str) -> Dict[str, Any]:
        """Generate local fallback responses for common commands when AI fails."""
        user_input_lower = user_input.lower()

        # Simple pattern matching for common commands
        if any(word in user_input_lower for word in ['create', 'make', 'add']) and 'channel' in user_input_lower:
            # Extract channel name if possible
            channel_name = "new-channel"
            if 'called' in user_input_lower:
                words = user_input.split()
                try:
                    called_index = [w.lower() for w in words].index('called')
                    if called_index + 1 < len(words):
                        channel_name = words[called_index + 1].strip('"\'')
                except ValueError:
                    pass

            return {
                "actions": [{
                    "type": "create_channel",  # Fixed: use correct action type
                    "name": channel_name,
                    "channel_type": "text",
                    "reason": "Local fallback for channel creation"
                }],
                "summary": f"Creating a new text channel: #{channel_name}",
                "intents": [{"intent_id": "local_1", "description": "Create channel", "priority": 1}]
            }

        elif any(word in user_input_lower for word in ['analyze', 'check', 'health']) and 'server' in user_input_lower:
            return {
                "actions": [{
                    "type": "analyze_server",
                    "reason": "Local fallback for server analysis"
                }],
                "summary": "Analyzing server structure and providing recommendations",
                "intents": [{"intent_id": "local_2", "description": "Analyze server", "priority": 1}]
            }

        elif any(word in user_input_lower for word in ['design', 'setup', 'build']) and any(word in user_input_lower for word in ['server', 'discord']):
            # Handle server design requests
            server_type = "general"
            if 'minecraft' in user_input_lower or 'smp' in user_input_lower:
                server_type = "minecraft"
            elif 'gaming' in user_input_lower:
                server_type = "gaming"

            return {
                "actions": [{
                    "type": "create_channel_template",
                    "template_name": server_type,
                    "category_name": "Main",
                    "reason": f"Local fallback for {server_type} server design"
                }],
                "summary": f"Creating {server_type} server structure with essential channels",
                "intents": [{"intent_id": "local_design", "description": f"Design {server_type} server", "priority": 1}]
            }

        elif any(word in user_input_lower for word in ['help', 'commands', 'what']):
            return {
                "actions": [{
                    "type": "send_message",
                    "content": "**Available Commands (AI service unavailable):**\n• Create channels: 'create a channel called [name]'\n• Analyze server: 'analyze my server'\n• Design server: 'design a discord server for [purpose]'\n• Get help: 'help'\n\nThe AI service is temporarily unavailable. Please try again later or use simpler commands.",
                    "reason": "Local help response"
                }],
                "summary": "Showing available commands",
                "intents": [{"intent_id": "local_3", "description": "Show help", "priority": 1}]
            }

        # Default fallback
        return {
            "actions": [{
                "type": "send_message",
                "content": "I'm currently experiencing technical difficulties. Please try:\n• Simpler commands\n• Rephrasing your request\n• Contacting an administrator\n\nExample: 'create a channel' or 'analyze my server'",
                "reason": "AI service unavailable"
            }],
            "summary": "AI service temporarily unavailable",
            "intents": [{"intent_id": "fallback", "description": "Service unavailable", "priority": 1}]
        }

    def _validate_and_enhance_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and enhance the AI response structure for multi-intent support."""
        enhanced = response.copy()

        # Ensure required fields exist
        if 'actions' not in enhanced:
            enhanced['actions'] = []
        if 'summary' not in enhanced:
            enhanced['summary'] = "Processing your request..."

        # Add missing multi-intent fields if not present
        if 'intents' not in enhanced:
            # Generate intents from actions if not provided
            enhanced['intents'] = self._generate_intents_from_actions(enhanced['actions'])

        if 'execution_plan' not in enhanced:
            enhanced['execution_plan'] = {
                "total_steps": len(enhanced['actions']),
                "estimated_time": f"{len(enhanced['actions']) * 15} seconds",
                "rollback_strategy": "delete_created_items",
                "risk_level": "low" if len(enhanced['actions']) <= 3 else "medium"
            }

        if 'dependencies' not in enhanced:
            enhanced['dependencies'] = self._generate_dependencies(enhanced['actions'])

        # Validate actions structure
        if not isinstance(enhanced['actions'], list):
            enhanced['actions'] = []

        # Add action_ids if missing
        for i, action in enumerate(enhanced['actions']):
            if 'action_id' not in action:
                action['action_id'] = f"action_{i+1}"

        return enhanced

    def _generate_intents_from_actions(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate intent structure from actions if not provided by AI."""
        intents = []
        intent_map = {}

        for i, action in enumerate(actions):
            action_type = action.get('type', 'unknown')

            # Group similar actions into intents
            intent_key = self._get_intent_category(action_type)

            if intent_key not in intent_map:
                intent_id = f"intent_{len(intents) + 1}"
                intent_map[intent_key] = intent_id
                intents.append({
                    "intent_id": intent_id,
                    "description": self._get_intent_description(intent_key),
                    "priority": len(intents) + 1,
                    "dependencies": []
                })

            # Link action to intent
            action['intent_id'] = intent_map[intent_key]

        return intents

    def _get_intent_category(self, action_type: str) -> str:
        """Categorize action types into intent groups."""
        category_map = {
            'create_channel': 'channel_creation',
            'create_category': 'category_creation',
            'create_role': 'role_creation',
            'edit_channel': 'channel_modification',
            'edit_channel_permissions': 'permission_management',
            'analyze_server': 'server_analysis',
            'reorganize_server_structure': 'server_reorganization',
            'merge_channels': 'channel_optimization',
            'auto_categorize': 'server_organization'
        }
        return category_map.get(action_type, 'general_action')

    def _get_intent_description(self, intent_category: str) -> str:
        """Get human-readable description for intent category."""
        descriptions = {
            'channel_creation': 'Create new channels',
            'category_creation': 'Create channel categories',
            'role_creation': 'Create server roles',
            'channel_modification': 'Modify channel properties',
            'permission_management': 'Manage permissions',
            'server_analysis': 'Analyze server structure',
            'server_reorganization': 'Reorganize server structure',
            'channel_optimization': 'Optimize channel organization',
            'server_organization': 'Organize server layout',
            'general_action': 'Perform server action'
        }
        return descriptions.get(intent_category, 'Perform server action')

    def _generate_dependencies(self, actions: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Generate action dependencies based on action types."""
        dependencies = {}

        for action in actions:
            action_id = action.get('action_id', 'unknown')
            action_type = action.get('type', 'unknown')
            dependencies[action_id] = []

            # Add dependencies based on action type logic
            if action_type in ['create_channel'] and action.get('category'):
                # Channel creation depends on category creation
                for other_action in actions:
                    if (other_action.get('type') == 'create_category' and
                        other_action.get('name') == action.get('category')):
                        dependencies[action_id].append(other_action.get('action_id', ''))

            elif action_type == 'reorganize_server_structure':
                # Reorganization depends on analysis
                for other_action in actions:
                    if other_action.get('type') == 'analyze_server':
                        dependencies[action_id].append(other_action.get('action_id', ''))

        return dependencies
    
    async def analyze_server(self, guild_data: Dict) -> Dict[str, Any]:
        """Analyze server structure and provide recommendations."""
        analysis_prompt = f"""
Analyze this Discord server structure and provide improvement suggestions.

SERVER DATA:
{json.dumps(guild_data, indent=2)}

Provide a JSON response with:
{{
  "actions": [
    {{
      "type": "analyze_server",
      "analysis": "detailed analysis of current structure",
      "missing_elements": ["list", "of", "missing", "elements"],
      "recommendations": ["list", "of", "specific", "recommendations"],
      "reason": "Server structure analysis"
    }}
  ],
  "summary": "Brief summary of analysis and key recommendations"
}}

Focus on:
1. Missing essential channels (welcome, rules, announcements)
2. Poor organization (channels not in categories)
3. Permission issues
4. Missing roles for moderation
5. Voice channel availability
6. Community engagement features

RESPOND WITH VALID JSON ONLY.
"""
        
        try:
            response = await self._generate_response(analysis_prompt)
            return json.loads(response)
        except Exception as e:
            bot_logger.error(f"Error in server analysis: {e}", exc_info=True)
            return {
                "actions": [{
                    "type": "analyze_server",
                    "analysis": "Unable to complete analysis due to an error.",
                    "missing_elements": [],
                    "recommendations": ["Please try the analysis again."],
                    "reason": "Analysis error"
                }],
                "summary": "Server analysis encountered an error."
            }

    def _test_api_connection(self):
        """Test Groq API connection with a simple request."""
        try:
            bot_logger.info("Testing Groq API connection...")

            # Simple test prompt
            test_response = self.client.chat.completions.create(
                messages=[
                    {"role": "user", "content": "Test connection. Respond with just 'OK'."}
                ],
                model=self.model_name,
                max_tokens=10,
                temperature=0
            )

            if test_response and test_response.choices:
                response_text = test_response.choices[0].message.content.strip()
                bot_logger.info(f"API connection test successful: {response_text}")
            else:
                bot_logger.warning("API connection test returned empty response")
                self._try_fallback_model()

        except Exception as e:
            bot_logger.error(f"API connection test failed: {e}")
            self._try_fallback_model()

    def _try_fallback_model(self):
        """Try switching to a more reliable fallback model."""
        fallback_models = [
            'llama-3.3-70b-versatile',
            'llama-3.1-70b-versatile',
            'mixtral-8x7b-32768'
        ]

        for fallback_model in fallback_models:
            if fallback_model != self.model_name:
                bot_logger.info(f"Trying fallback model: {fallback_model}")
                self.model_name = fallback_model
                try:
                    # Test the fallback model
                    test_response = self.client.chat.completions.create(
                        messages=[
                            {"role": "user", "content": "Test. Respond 'OK'."}
                        ],
                        model=self.model_name,
                        max_tokens=5,
                        temperature=0
                    )

                    if test_response and test_response.choices:
                        bot_logger.info(f"Successfully switched to fallback model: {fallback_model}")
                        return

                except Exception as e:
                    bot_logger.warning(f"Fallback model {fallback_model} also failed: {e}")
                    continue

        bot_logger.error("All fallback models failed. Bot will use local fallback responses.")

    async def classify_request(self, user_input: str) -> Dict[str, Any]:
        """
        Stage 1: Classify user request into SIMPLE, COMPLEX, or CREATIVE categories.
        Returns classification with confidence score and reasoning.
        """
        try:
            start_time = time.time()
            bot_logger.debug(f"Classifying request: {user_input[:100]}...")

            # Create classification prompt
            classification_prompt = f"{self.classification_prompt}\n\nUser Request: {user_input}"

            # Get classification from AI
            response = await self._generate_response(classification_prompt)
            parsing_time = time.time() - start_time

            # Parse classification response
            try:
                classification_data = json.loads(response)

                # Validate classification
                valid_classifications = ["SIMPLE", "COMPLEX", "CREATIVE"]
                if classification_data.get("classification") not in valid_classifications:
                    bot_logger.warning(f"Invalid classification: {classification_data.get('classification')}")
                    # Default to COMPLEX for safety
                    classification_data = {
                        "classification": "COMPLEX",
                        "confidence_score": 0.5,
                        "reasoning": "Invalid classification, defaulting to COMPLEX"
                    }

                # Ensure confidence score is valid
                confidence = classification_data.get("confidence_score", 0.5)
                if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
                    classification_data["confidence_score"] = 0.5

                bot_logger.info(f"Request classified as {classification_data['classification']} "
                              f"(confidence: {classification_data['confidence_score']:.2f}) "
                              f"in {parsing_time:.3f}s")

                return classification_data

            except json.JSONDecodeError as e:
                bot_logger.error(f"Failed to parse classification response: {e}")
                # Fallback classification based on simple heuristics
                return self._get_heuristic_classification(user_input)

        except Exception as e:
            bot_logger.error(f"Error in request classification: {e}")
            return self._get_heuristic_classification(user_input)

    def _get_heuristic_classification(self, user_input: str) -> Dict[str, Any]:
        """Fallback classification using simple heuristics when AI fails."""
        user_lower = user_input.lower()

        # SIMPLE patterns
        simple_patterns = [
            'create a channel', 'delete', 'remove', 'add role', 'set slowmode',
            'rename', 'move', 'lock', 'unlock', 'mute', 'unmute'
        ]

        # CREATIVE patterns
        creative_patterns = [
            'design', 'suggest', 'improve', 'reorganize', 'optimize', 'recommend',
            'help me', 'what should', 'how can i', 'make better', 'professional'
        ]

        # Check for creative patterns first
        if any(pattern in user_lower for pattern in creative_patterns):
            return {
                "classification": "CREATIVE",
                "confidence_score": 0.7,
                "reasoning": "Heuristic: Contains creative/design keywords"
            }

        # Check for simple patterns
        elif any(pattern in user_lower for pattern in simple_patterns) and len(user_input.split()) <= 8:
            return {
                "classification": "SIMPLE",
                "confidence_score": 0.8,
                "reasoning": "Heuristic: Simple action pattern with short length"
            }

        # Default to COMPLEX
        else:
            return {
                "classification": "COMPLEX",
                "confidence_score": 0.6,
                "reasoning": "Heuristic: Default to COMPLEX for safety"
            }

    async def process_simple_request(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Stage 2A: Process SIMPLE requests with lightweight pattern matching.
        Target: <0.5 seconds response time.
        """
        start_time = time.time()
        bot_logger.debug("Processing SIMPLE request with lightweight parsing")

        # Use local pattern matching for common simple actions
        result = self._parse_simple_patterns(user_input, server_context)

        if result:
            processing_time = time.time() - start_time
            bot_logger.info(f"SIMPLE request processed locally in {processing_time:.3f}s")
            return result

        # Fallback to minimal AI processing for unrecognized simple requests
        simple_prompt = f"""
Parse this simple Discord server management request and respond with JSON:

Request: {user_input}

Respond with:
{{
  "actions": [{{
    "type": "action_type",
    "name": "target_name",
    "reason": "brief_reason"
  }}],
  "summary": "Brief action description",
  "intents": [{{"intent_id": "simple_1", "description": "action description", "priority": 1}}]
}}
"""

        try:
            response = await self._generate_response(simple_prompt)
            result = json.loads(response)

            processing_time = time.time() - start_time
            bot_logger.info(f"SIMPLE request processed with AI in {processing_time:.3f}s")
            return result

        except Exception as e:
            bot_logger.error(f"Error processing simple request: {e}")
            return self._get_local_fallback_response(user_input)

    def _parse_simple_patterns(self, user_input: str, server_context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse simple requests using local pattern matching for speed."""
        user_lower = user_input.lower()

        # Channel creation patterns
        if 'create' in user_lower and 'channel' in user_lower:
            channel_name = self._extract_name_from_input(user_input, 'channel')
            return {
                "actions": [{
                    "type": "create_channel",
                    "name": channel_name,
                    "channel_type": "text",
                    "reason": "Simple channel creation request"
                }],
                "summary": f"Creating text channel: #{channel_name}",
                "intents": [{"intent_id": "simple_create", "description": "Create channel", "priority": 1}],
                "execution_plan": {
                    "total_steps": 1,
                    "estimated_time": "5 seconds",
                    "estimated_complexity": "low",
                    "requires_confirmation": False
                }
            }

        # Role creation patterns
        elif 'create' in user_lower and 'role' in user_lower:
            role_name = self._extract_name_from_input(user_input, 'role')
            return {
                "actions": [{
                    "type": "create_role",
                    "name": role_name,
                    "reason": "Simple role creation request"
                }],
                "summary": f"Creating role: @{role_name}",
                "intents": [{"intent_id": "simple_role", "description": "Create role", "priority": 1}],
                "execution_plan": {
                    "total_steps": 1,
                    "estimated_time": "3 seconds",
                    "estimated_complexity": "low",
                    "requires_confirmation": False
                }
            }

        # Delete patterns
        elif 'delete' in user_lower or 'remove' in user_lower:
            if 'channel' in user_lower:
                channel_name = self._extract_name_from_input(user_input, 'channel')
                return {
                    "actions": [{
                        "type": "delete_channel",
                        "name": channel_name,
                        "reason": "Simple channel deletion request"
                    }],
                    "summary": f"Deleting channel: #{channel_name}",
                    "intents": [{"intent_id": "simple_delete", "description": "Delete channel", "priority": 1}],
                    "execution_plan": {
                        "total_steps": 1,
                        "estimated_time": "3 seconds",
                        "estimated_complexity": "low",
                        "requires_confirmation": True
                    }
                }

        return None

    def _extract_name_from_input(self, user_input: str, target_type: str) -> str:
        """Extract names from user input using simple patterns."""
        words = user_input.split()

        # Look for patterns like "create a channel called general"
        if 'called' in user_input.lower():
            try:
                called_index = [w.lower() for w in words].index('called')
                if called_index + 1 < len(words):
                    return words[called_index + 1].strip('"\'#@')
            except ValueError:
                pass

        # Look for patterns like "create #general channel"
        for word in words:
            if word.startswith('#') and target_type == 'channel':
                return word[1:]
            elif word.startswith('@') and target_type == 'role':
                return word[1:]

        # Default names
        return f"new-{target_type}"

    async def process_creative_request(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Stage 2C: Process CREATIVE requests with full AI capabilities and server analysis.
        Target: <5 seconds response time.
        """
        start_time = time.time()
        bot_logger.debug("Processing CREATIVE request with full AI analysis")

        # Enhanced creative prompt with server context
        creative_prompt = f"""
You are an expert Discord server designer and community manager. Analyze the current server state and provide creative, intelligent recommendations.

CURRENT SERVER CONTEXT:
{json.dumps(server_context, indent=2)}

USER REQUEST: {user_input}

Provide a comprehensive response with creative analysis and actionable recommendations. Include:

1. Server analysis and assessment
2. Creative design rationale
3. Specific implementation steps
4. Alternative suggestions
5. Expected community impact

Respond with JSON:
{{
  "actions": [
    {{
      "type": "action_type",
      "name": "specific_name",
      "description": "detailed_description",
      "reason": "design_rationale"
    }}
  ],
  "summary": "Creative solution overview",
  "intents": [
    {{
      "intent_id": "creative_1",
      "description": "main creative intent",
      "priority": 1,
      "dependencies": []
    }}
  ],
  "execution_plan": {{
    "total_steps": 5,
    "estimated_time": "2 minutes",
    "estimated_complexity": "high",
    "requires_confirmation": true
  }},
  "creative_elements": {{
    "server_analysis": {{
      "current_strengths": ["strength1", "strength2"],
      "improvement_areas": ["area1", "area2"],
      "community_size_assessment": "small|medium|large",
      "organization_level": "basic|intermediate|advanced"
    }},
    "design_rationale": "Detailed explanation of why this design approach was chosen",
    "alternative_suggestions": [
      {{
        "approach": "Alternative approach name",
        "description": "What this alternative would involve",
        "pros": ["benefit1", "benefit2"],
        "cons": ["limitation1", "limitation2"]
      }}
    ],
    "expected_impact": {{
      "user_engagement": "Expected change in engagement",
      "organization_improvement": "How this improves server organization",
      "moderation_benefits": "Benefits for server moderation"
    }}
  }}
}}
"""

        try:
            response = await self._generate_response(creative_prompt)
            result = json.loads(response)

            # Ensure creative_elements section exists
            if "creative_elements" not in result:
                result["creative_elements"] = self._generate_fallback_creative_elements(user_input, server_context)

            processing_time = time.time() - start_time
            bot_logger.info(f"CREATIVE request processed in {processing_time:.3f}s")

            return result

        except Exception as e:
            bot_logger.error(f"Error processing creative request: {e}")
            return self._get_creative_fallback_response(user_input, server_context)

    def _generate_fallback_creative_elements(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate fallback creative elements when AI processing fails."""
        return {
            "server_analysis": {
                "current_strengths": ["Active community", "Basic organization"],
                "improvement_areas": ["Channel organization", "Role structure"],
                "community_size_assessment": "medium",
                "organization_level": "intermediate"
            },
            "design_rationale": f"Based on the request '{user_input}', implementing a structured approach to improve server organization and user experience.",
            "alternative_suggestions": [
                {
                    "approach": "Gradual Implementation",
                    "description": "Implement changes in phases to minimize disruption",
                    "pros": ["Less disruptive", "Easier to manage"],
                    "cons": ["Takes longer", "May lose momentum"]
                }
            ],
            "expected_impact": {
                "user_engagement": "Improved navigation and clearer purpose for each area",
                "organization_improvement": "Better categorization and logical flow",
                "moderation_benefits": "Clearer channel purposes and easier oversight"
            }
        }

    def _get_creative_fallback_response(self, user_input: str, server_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate creative fallback response when AI fails."""
        return {
            "actions": [{
                "type": "send_message",
                "content": f"I understand you want creative help with: '{user_input}'. While my AI creativity engine is temporarily unavailable, I can still help with specific actions. Try asking for specific changes like 'create channels for gaming' or 'analyze my server structure'.",
                "reason": "Creative AI temporarily unavailable"
            }],
            "summary": "Creative assistance temporarily limited",
            "intents": [{"intent_id": "creative_fallback", "description": "Creative fallback", "priority": 1}],
            "execution_plan": {
                "total_steps": 1,
                "estimated_time": "immediate",
                "estimated_complexity": "low",
                "requires_confirmation": False
            },
            "creative_elements": self._generate_fallback_creative_elements(user_input, server_context)
        }

# Global AI service instance
ai_service = AIService()
