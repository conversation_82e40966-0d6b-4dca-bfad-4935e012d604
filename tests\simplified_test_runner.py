"""
Simplified automated testing that bypasses AI service and tests core logic directly.
"""
import asyncio
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tests.mock_discord import MockGuild, create_test_guild
from src.services.server_context import ServerContextService
from src.services.response_formatter import ResponseFormatter
from src.services.context_memory import ContextualMemorySystem

class SimplifiedTestRunner:
    """Simplified test runner that tests core functionality without AI service."""
    
    def __init__(self):
        self.test_results = []
        self.context_service = ServerContextService()
        self.response_formatter = ResponseFormatter()
        self.memory_system = ContextualMemorySystem("tests/test_context_memory.json")
    
    async def test_server_context_service(self):
        """Test server context gathering."""
        print("🧪 Testing Server Context Service...")
        
        try:
            # Test with simple guild
            guild = create_test_guild("simple")
            start_time = time.time()
            context = await self.context_service.get_server_context(guild)
            response_time = time.time() - start_time
            
            # Validate context structure (using actual field names)
            required_fields = ["server_info", "channels", "roles", "permissions_summary"]
            success = all(field in context for field in required_fields)
            
            print(f"✅ Server Context: {success}, Response Time: {response_time:.3f}s")
            print(f"   Channels: {len(context.get('channels', []))}")
            print(f"   Roles: {len(context.get('roles', []))}")
            print(f"   Server Info: {context.get('server_info', {}).get('name', 'N/A')}")
            
            self.test_results.append({
                "test": "Server Context Service",
                "success": success,
                "response_time": response_time,
                "details": f"Processed {len(context.get('channels', []))} channels, {len(context.get('roles', []))} roles"
            })
            
        except Exception as e:
            print(f"❌ Server Context Test Failed: {e}")
            self.test_results.append({
                "test": "Server Context Service",
                "success": False,
                "response_time": 0,
                "error": str(e)
            })
    
    async def test_response_formatter(self):
        """Test response formatting."""
        print("\n🧪 Testing Response Formatter...")
        
        try:
            # Create mock parsed result
            mock_parsed_result = {
                "intents": [
                    {"intent_id": "intent_1", "description": "Create Support category", "priority": 1},
                    {"intent_id": "intent_2", "description": "Add help channels", "priority": 2}
                ],
                "actions": [
                    {"type": "create_category", "name": "Support"},
                    {"type": "create_text_channel", "name": "help-desk", "category": "Support"},
                    {"type": "create_role", "name": "Support", "permissions": ["manage_messages"]}
                ],
                "execution_plan": {
                    "total_steps": 3,
                    "estimated_time": "30 seconds",
                    "risk_level": "low"
                }
            }
            
            mock_execution_results = (
                ["Created Support category", "Created #help-desk channel", "Created @Support role"],
                [],
                {"total_actions": 3, "completed_actions": 3}
            )
            
            start_time = time.time()
            response = self.response_formatter.format_multi_step_response(
                "Create a support system",
                mock_parsed_result,
                mock_execution_results
            )
            response_time = time.time() - start_time
            
            # Validate response quality
            quality_indicators = [
                "Request Summary" in response,
                "My Understanding" in response,
                "Execution Results" in response,
                len(response) > 100,
                "✅" in response
            ]
            
            success = sum(quality_indicators) >= 4
            
            print(f"✅ Response Formatter: {success}, Response Time: {response_time:.3f}s")
            print(f"   Response Length: {len(response)} characters")
            print(f"   Quality Indicators: {sum(quality_indicators)}/5")
            
            self.test_results.append({
                "test": "Response Formatter",
                "success": success,
                "response_time": response_time,
                "details": f"Generated {len(response)} character response with {sum(quality_indicators)}/5 quality indicators"
            })
            
        except Exception as e:
            print(f"❌ Response Formatter Test Failed: {e}")
            self.test_results.append({
                "test": "Response Formatter",
                "success": False,
                "response_time": 0,
                "error": str(e)
            })
    
    async def test_contextual_memory(self):
        """Test contextual memory system."""
        print("\n🧪 Testing Contextual Memory System...")
        
        try:
            guild_id = 12345
            user_id = 67890
            
            # Test storing command
            start_time = time.time()
            mock_parsed_response = {
                "intents": [{"intent_id": "intent_1", "description": "Create gaming category"}],
                "actions": [{"type": "create_category", "name": "Gaming"}]
            }
            execution_results = (["Created Gaming category"], [], {"success": True})
            self.memory_system.add_command_to_history(
                guild_id, user_id, "Create gaming category", mock_parsed_response, execution_results
            )
            
            # Test retrieving context
            context = self.memory_system.get_conversation_context(guild_id, user_id)
            response_time = time.time() - start_time
            
            # Test follow-up detection
            follow_up_result = self.memory_system.detect_follow_up_context(
                "Also add a voice channel there", context
            )
            follow_up_detected = follow_up_result.get("is_follow_up", False)
            
            success = context is not None and follow_up_detected
            
            print(f"✅ Contextual Memory: {success}, Response Time: {response_time:.3f}s")
            print(f"   Context Retrieved: {context is not None}")
            print(f"   Follow-up Detected: {follow_up_detected}")
            
            self.test_results.append({
                "test": "Contextual Memory System",
                "success": success,
                "response_time": response_time,
                "details": f"Context storage/retrieval working, follow-up detection: {follow_up_detected}"
            })
            
        except Exception as e:
            print(f"❌ Contextual Memory Test Failed: {e}")
            self.test_results.append({
                "test": "Contextual Memory System",
                "success": False,
                "response_time": 0,
                "error": str(e)
            })
    
    async def test_mock_discord_objects(self):
        """Test mock Discord objects."""
        print("\n🧪 Testing Mock Discord Objects...")
        
        try:
            start_time = time.time()
            
            # Test different guild types
            simple_guild = create_test_guild("simple")
            complex_guild = create_test_guild("complex")
            messy_guild = create_test_guild("messy")
            
            response_time = time.time() - start_time
            
            # Validate guild structures
            guilds_valid = all([
                hasattr(simple_guild, 'created_at'),
                hasattr(complex_guild, 'channels'),
                hasattr(messy_guild, 'roles'),
                len(complex_guild.channels) > len(simple_guild.channels),
                len(messy_guild.text_channels) > 5
            ])
            
            print(f"✅ Mock Discord Objects: {guilds_valid}, Response Time: {response_time:.3f}s")
            print(f"   Simple Guild: {len(simple_guild.channels)} channels")
            print(f"   Complex Guild: {len(complex_guild.channels)} channels")
            print(f"   Messy Guild: {len(messy_guild.channels)} channels")
            
            self.test_results.append({
                "test": "Mock Discord Objects",
                "success": guilds_valid,
                "response_time": response_time,
                "details": f"Created 3 guild types with varying complexity"
            })
            
        except Exception as e:
            print(f"❌ Mock Discord Objects Test Failed: {e}")
            self.test_results.append({
                "test": "Mock Discord Objects",
                "success": False,
                "response_time": 0,
                "error": str(e)
            })
    
    async def run_all_tests(self):
        """Run all simplified tests."""
        print("🚀 SIMPLIFIED AUTOMATED TEST SUITE")
        print("=" * 80)
        print(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("Testing core functionality without AI service dependency...")
        print()
        
        # Run all tests
        await self.test_mock_discord_objects()
        await self.test_server_context_service()
        await self.test_response_formatter()
        await self.test_contextual_memory()
        
        # Generate summary
        print("\n📊 TEST SUMMARY")
        print("=" * 40)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        avg_response_time = sum(result["response_time"] for result in self.test_results) / total_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Average Response Time: {avg_response_time:.3f}s")
        
        print("\n📋 DETAILED RESULTS")
        print("-" * 40)
        for result in self.test_results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"{status} {result['test']}: {result['response_time']:.3f}s")
            if "details" in result:
                print(f"    {result['details']}")
            if "error" in result:
                print(f"    Error: {result['error']}")
        
        # Assessment
        success_rate = passed_tests / total_tests
        if success_rate >= 0.9:
            rating = "EXCELLENT"
        elif success_rate >= 0.7:
            rating = "GOOD"
        elif success_rate >= 0.5:
            rating = "FAIR"
        else:
            rating = "POOR"
        
        print(f"\n🎯 OVERALL RATING: {rating}")
        
        if rating == "EXCELLENT":
            print("✅ Core systems are working optimally")
        elif rating == "GOOD":
            print("⚠️ Core systems mostly functional with minor issues")
        else:
            print("❌ Core systems need attention")
        
        return rating

async def main():
    """Run simplified test suite."""
    runner = SimplifiedTestRunner()
    rating = await runner.run_all_tests()
    
    if rating == "EXCELLENT":
        return 0
    elif rating in ["GOOD", "FAIR"]:
        return 1
    else:
        return 2

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(result)
