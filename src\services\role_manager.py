"""
Advanced role management service for Discord servers.
"""
import discord
from typing import Dict, List, Optional, Tuple
from src.utils.logger import bot_logger

class RoleManager:
    """Advanced role management operations."""
    
    def __init__(self, bot):
        self.bot = bot
    
    async def create_role_hierarchy(self, guild: discord.Guild, hierarchy_type: str) -> List[str]:
        """Create a complete role hierarchy based on server type."""
        hierarchies = {
            "gaming_community": [
                {"name": "Owner", "color": discord.Color.red(), "permissions": ["administrator"], "hoist": True},
                {"name": "Admin", "color": discord.Color.orange(), "permissions": ["manage_guild", "manage_channels", "manage_roles", "kick_members", "ban_members"], "hoist": True},
                {"name": "Moderator", "color": discord.Color.blue(), "permissions": ["manage_messages", "kick_members", "mute_members"], "hoist": True},
                {"name": "VIP", "color": discord.Color.gold(), "permissions": [], "hoist": True},
                {"name": "Member", "color": discord.Color.green(), "permissions": [], "hoist": False},
                {"name": "New Member", "color": discord.Color.light_grey(), "permissions": [], "hoist": False}
            ],
            "professional_workspace": [
                {"name": "CEO", "color": discord.Color.red(), "permissions": ["administrator"], "hoist": True},
                {"name": "Management", "color": discord.Color.orange(), "permissions": ["manage_guild", "manage_channels", "manage_roles"], "hoist": True},
                {"name": "Team Lead", "color": discord.Color.blue(), "permissions": ["manage_messages"], "hoist": True},
                {"name": "Employee", "color": discord.Color.green(), "permissions": [], "hoist": False},
                {"name": "Contractor", "color": discord.Color.purple(), "permissions": [], "hoist": False},
                {"name": "Guest", "color": discord.Color.light_grey(), "permissions": [], "hoist": False}
            ],
            "study_group": [
                {"name": "Teacher", "color": discord.Color.red(), "permissions": ["administrator"], "hoist": True},
                {"name": "Teaching Assistant", "color": discord.Color.orange(), "permissions": ["manage_messages", "manage_channels"], "hoist": True},
                {"name": "Student", "color": discord.Color.blue(), "permissions": [], "hoist": False},
                {"name": "Visitor", "color": discord.Color.light_grey(), "permissions": [], "hoist": False}
            ]
        }
        
        if hierarchy_type not in hierarchies:
            raise ValueError(f"Unknown hierarchy type: {hierarchy_type}")
        
        hierarchy = hierarchies[hierarchy_type]
        results = []
        
        # Create roles in reverse order (lowest first) to maintain hierarchy
        for role_config in reversed(hierarchy):
            try:
                # Check if role already exists
                existing_role = discord.utils.get(guild.roles, name=role_config["name"])
                if existing_role:
                    results.append(f"Role '{role_config['name']}' already exists")
                    continue
                
                # Build permissions
                permissions = discord.Permissions()
                for perm_name in role_config.get("permissions", []):
                    if hasattr(permissions, perm_name):
                        setattr(permissions, perm_name, True)
                
                # Create role
                role = await guild.create_role(
                    name=role_config["name"],
                    color=role_config.get("color", discord.Color.default()),
                    permissions=permissions,
                    hoist=role_config.get("hoist", False),
                    reason=f"Created as part of {hierarchy_type} hierarchy"
                )
                
                results.append(f"Created role '{role.name}'")
                
            except Exception as e:
                bot_logger.error(f"Failed to create role {role_config['name']}: {e}")
                results.append(f"Failed to create role '{role_config['name']}': {str(e)}")
        
        return results
    
    async def assign_role_colors(self, guild: discord.Guild, color_scheme: str) -> List[str]:
        """Assign colors to roles based on a color scheme."""
        color_schemes = {
            "rainbow": [
                discord.Color.red(),
                discord.Color.orange(),
                discord.Color.gold(),
                discord.Color.green(),
                discord.Color.blue(),
                discord.Color.purple(),
                discord.Color.magenta()
            ],
            "professional": [
                discord.Color.dark_blue(),
                discord.Color.blue(),
                discord.Color.teal(),
                discord.Color.dark_green(),
                discord.Color.green(),
                discord.Color.dark_grey(),
                discord.Color.light_grey()
            ],
            "warm": [
                discord.Color.red(),
                discord.Color.orange(),
                discord.Color.gold(),
                discord.Color.yellow(),
                discord.Color.from_rgb(255, 192, 203),  # Pink
                discord.Color.from_rgb(255, 165, 0),    # Orange
                discord.Color.from_rgb(255, 69, 0)      # Red-orange
            ]
        }
        
        if color_scheme not in color_schemes:
            raise ValueError(f"Unknown color scheme: {color_scheme}")
        
        colors = color_schemes[color_scheme]
        results = []
        
        # Get non-admin roles (excluding @everyone and bot roles)
        eligible_roles = [role for role in guild.roles 
                         if (role != guild.default_role and 
                             not role.managed and 
                             not role.permissions.administrator)]
        
        # Sort by position (highest first)
        eligible_roles.sort(key=lambda r: r.position, reverse=True)
        
        # Assign colors
        for i, role in enumerate(eligible_roles):
            if i < len(colors):
                try:
                    await role.edit(color=colors[i], reason=f"Applied {color_scheme} color scheme")
                    results.append(f"Applied color to role '{role.name}'")
                except Exception as e:
                    bot_logger.error(f"Failed to color role {role.name}: {e}")
                    results.append(f"Failed to color role '{role.name}': {str(e)}")
        
        return results if results else ["No eligible roles found for coloring"]
    
    async def create_reaction_roles(self, guild: discord.Guild, channel_name: str, 
                                  role_emoji_mapping: Dict[str, str]) -> str:
        """Create a reaction role system in a specified channel."""
        channel = discord.utils.get(guild.text_channels, name=channel_name)
        if not channel:
            raise ValueError(f"Channel '{channel_name}' not found")
        
        # Create embed for reaction roles
        embed = discord.Embed(
            title="🎭 Reaction Roles",
            description="React to this message to get roles!",
            color=discord.Color.blue()
        )
        
        role_list = []
        for emoji, role_name in role_emoji_mapping.items():
            role = discord.utils.get(guild.roles, name=role_name)
            if role:
                role_list.append(f"{emoji} - {role.mention}")
            else:
                # Create role if it doesn't exist
                role = await guild.create_role(name=role_name, reason="Created for reaction roles")
                role_list.append(f"{emoji} - {role.mention}")
        
        embed.add_field(
            name="Available Roles",
            value="\n".join(role_list),
            inline=False
        )
        
        # Send message and add reactions
        message = await channel.send(embed=embed)
        for emoji in role_emoji_mapping.keys():
            try:
                await message.add_reaction(emoji)
            except discord.HTTPException:
                bot_logger.warning(f"Failed to add reaction {emoji}")
        
        return f"Created reaction role system in #{channel.name}"
    
    async def cleanup_unused_roles(self, guild: discord.Guild) -> List[str]:
        """Remove roles that have no members and aren't important."""
        results = []
        
        # Get roles with no members (excluding @everyone, admin roles, and bot roles)
        unused_roles = []
        for role in guild.roles:
            if (role != guild.default_role and 
                not role.managed and 
                not role.permissions.administrator and
                len(role.members) == 0):
                unused_roles.append(role)
        
        if not unused_roles:
            return ["No unused roles found"]
        
        # Delete unused roles
        for role in unused_roles:
            try:
                await role.delete(reason="Cleanup - role has no members")
                results.append(f"Deleted unused role '{role.name}'")
            except Exception as e:
                bot_logger.error(f"Failed to delete role {role.name}: {e}")
                results.append(f"Failed to delete role '{role.name}': {str(e)}")
        
        return results
    
    async def organize_role_hierarchy(self, guild: discord.Guild) -> List[str]:
        """Organize roles in a logical hierarchy based on permissions."""
        results = []
        
        # Get all non-managed roles (excluding @everyone)
        roles_to_organize = [role for role in guild.roles 
                           if role != guild.default_role and not role.managed]
        
        # Sort roles by permission level
        def permission_weight(role):
            perms = role.permissions
            weight = 0
            if perms.administrator:
                weight += 1000
            if perms.manage_guild:
                weight += 500
            if perms.manage_channels or perms.manage_roles:
                weight += 250
            if perms.kick_members or perms.ban_members:
                weight += 100
            if perms.manage_messages:
                weight += 50
            return weight
        
        roles_to_organize.sort(key=permission_weight, reverse=True)
        
        # Reorder roles
        for i, role in enumerate(roles_to_organize):
            try:
                # Calculate new position (higher permission = higher position)
                new_position = len(guild.roles) - i - 1
                if role.position != new_position:
                    await role.edit(position=new_position, reason="Role hierarchy organization")
                    results.append(f"Moved role '{role.name}' to position {new_position}")
            except Exception as e:
                bot_logger.error(f"Failed to move role {role.name}: {e}")
                results.append(f"Failed to move role '{role.name}': {str(e)}")
        
        return results if results else ["Role hierarchy is already organized"]
    
    async def bulk_assign_role(self, guild: discord.Guild, role_name: str, 
                             criteria: str) -> List[str]:
        """Bulk assign a role to members based on criteria."""
        role = discord.utils.get(guild.roles, name=role_name)
        if not role:
            raise ValueError(f"Role '{role_name}' not found")
        
        results = []
        assigned_count = 0
        
        # Define criteria functions
        if criteria == "no_roles":
            # Assign to members with only @everyone role
            targets = [member for member in guild.members 
                      if len(member.roles) == 1 and not member.bot]
        elif criteria == "new_members":
            # Assign to members who joined in the last 7 days
            import datetime
            week_ago = datetime.datetime.now() - datetime.timedelta(days=7)
            targets = [member for member in guild.members 
                      if member.joined_at and member.joined_at > week_ago and not member.bot]
        elif criteria == "active_members":
            # This would require tracking activity, for now just return empty
            targets = []
            results.append("Active member detection not implemented yet")
        else:
            raise ValueError(f"Unknown criteria: {criteria}")
        
        # Assign role to targets
        for member in targets:
            try:
                if role not in member.roles:
                    await member.add_roles(role, reason=f"Bulk assignment - {criteria}")
                    assigned_count += 1
            except Exception as e:
                bot_logger.error(f"Failed to assign role to {member}: {e}")
        
        if assigned_count > 0:
            results.append(f"Assigned role '{role_name}' to {assigned_count} members")
        else:
            results.append(f"No members matched criteria '{criteria}' for role assignment")
        
        return results
