"""
Enhanced response formatting system for Discord Server Management Bot.
Provides structured, user-friendly responses with progress indicators and actionable feedback.
"""
import discord
from typing import Dict, List, Any, Optional
from datetime import datetime
from src.utils.logger import bot_logger

class ResponseFormatter:
    """Formats bot responses with enhanced structure and user experience."""
    
    @staticmethod
    def format_multi_step_response(
        user_request: str,
        parsed_response: Dict[str, Any],
        execution_results: tuple,
        execution_time: float = 0.0
    ) -> str:
        """
        Format a comprehensive response for multi-step operations.
        
        Args:
            user_request: Original user request
            parsed_response: Parsed AI response with intents and actions
            execution_results: Tuple of (success_messages, error_messages, execution_summary)
            execution_time: Total execution time in seconds
            
        Returns:
            Formatted response string
        """
        success_messages, error_messages, execution_summary = execution_results
        
        # Build structured response
        response_parts = []
        
        # Header with request summary
        response_parts.append("📝 **Request Summary:**")
        response_parts.append(f"_{user_request}_\n")
        
        # AI understanding section
        intents = parsed_response.get('intents', [])
        if intents:
            response_parts.append("🧠 **My Understanding:**")
            for intent in intents:
                priority_emoji = "🥇" if intent.get('priority', 1) == 1 else "🥈" if intent.get('priority', 1) == 2 else "🥉"
                response_parts.append(f"{priority_emoji} {intent.get('description', 'Unknown intent')}")
            response_parts.append("")
        
        # Execution plan
        execution_plan = parsed_response.get('execution_plan', {})
        if execution_plan:
            response_parts.append("📋 **Execution Plan:**")
            response_parts.append(f"• **Steps:** {execution_plan.get('total_steps', 'Unknown')}")
            response_parts.append(f"• **Estimated Time:** {execution_plan.get('estimated_time', 'Unknown')}")
            response_parts.append(f"• **Risk Level:** {execution_plan.get('risk_level', 'Unknown').title()}")
            response_parts.append("")
        
        # Execution results
        response_parts.append("⚡ **Execution Results:**")
        
        # Success section
        if success_messages:
            response_parts.append("✅ **Successful Actions:**")
            for i, message in enumerate(success_messages, 1):
                response_parts.append(f"  {i}. {message}")
            response_parts.append("")
        
        # Partial success/warnings
        if error_messages and success_messages:
            response_parts.append("⚠️ **Issues Encountered:**")
            for i, error in enumerate(error_messages, 1):
                response_parts.append(f"  {i}. {error}")
            response_parts.append("")
        
        # Complete failure
        elif error_messages and not success_messages:
            response_parts.append("❌ **Failed Actions:**")
            for i, error in enumerate(error_messages, 1):
                response_parts.append(f"  {i}. {error}")
            response_parts.append("")
        
        # Impact summary
        if execution_summary:
            response_parts.append("📊 **Impact Summary:**")
            total_actions = execution_summary.get('total_actions', 0)
            completed = execution_summary.get('completed_actions', 0)
            failed = execution_summary.get('failed_actions', 0)
            
            if completed > 0:
                response_parts.append(f"• **Completed:** {completed}/{total_actions} actions")
            if failed > 0:
                response_parts.append(f"• **Failed:** {failed}/{total_actions} actions")
            if execution_summary.get('rollback_performed'):
                response_parts.append("• **Rollback:** Performed due to critical failures")
            
            actual_time = execution_summary.get('execution_time', execution_time)
            if actual_time > 0:
                response_parts.append(f"• **Execution Time:** {actual_time:.1f} seconds")
            response_parts.append("")
        
        # Next steps or suggestions
        if error_messages:
            response_parts.append("🔧 **Next Steps:**")
            if "permission" in " ".join(error_messages).lower():
                response_parts.append("• Check that I have the necessary permissions for these actions")
            if "not found" in " ".join(error_messages).lower():
                response_parts.append("• Verify that the mentioned channels/roles exist")
            if execution_summary.get('rollback_performed'):
                response_parts.append("• Review the failed actions and try again with simpler steps")
            else:
                response_parts.append("• You can try the failed actions individually for more specific error details")
        
        return "\n".join(response_parts)
    
    @staticmethod
    def format_progress_update(current_step: int, total_steps: int, action_description: str) -> str:
        """Format a progress update message."""
        progress_bar = ResponseFormatter._create_progress_bar(current_step, total_steps)
        percentage = int((current_step / total_steps) * 100)
        
        return f"🔄 **Progress:** {progress_bar} {percentage}%\n📝 **Current Step:** {action_description}"
    
    @staticmethod
    def _create_progress_bar(current: int, total: int, length: int = 10) -> str:
        """Create a visual progress bar."""
        filled = int((current / total) * length)
        bar = "█" * filled + "░" * (length - filled)
        return f"[{bar}]"
    
    @staticmethod
    def format_analysis_response(analysis_data: Dict[str, Any]) -> str:
        """Format server analysis response with enhanced visuals."""
        if 'error' in analysis_data:
            return f"❌ **Analysis Error:** {analysis_data['error']}"
        
        response_parts = []
        
        # Header
        response_parts.append("🏥 **Server Health Analysis**\n")
        
        # Overall score with visual indicator
        overall_score = analysis_data.get('overall_score', 0)
        score_emoji = "🟢" if overall_score >= 80 else "🟡" if overall_score >= 60 else "🔴"
        response_parts.append(f"**Overall Health:** {score_emoji} {overall_score:.1f}/100\n")
        
        # Category breakdown
        category_scores = analysis_data.get('category_scores', {})
        if category_scores:
            response_parts.append("📊 **Category Breakdown:**")
            for category, score in category_scores.items():
                emoji = "🟢" if score >= 80 else "🟡" if score >= 60 else "🔴"
                bar = ResponseFormatter._create_score_bar(score)
                response_parts.append(f"{emoji} **{category.title()}:** {bar} {score:.1f}/100")
            response_parts.append("")
        
        # Server type
        server_type = analysis_data.get('server_type', 'unknown')
        if server_type != 'unknown':
            type_emoji = {"gaming": "🎮", "business": "💼", "education": "📚", "community": "👥"}.get(server_type, "🏢")
            response_parts.append(f"🎯 **Server Type:** {type_emoji} {server_type.title()}\n")
        
        # Strengths
        strengths = analysis_data.get('strengths', [])
        if strengths:
            response_parts.append("💪 **Strengths:**")
            for strength in strengths:
                response_parts.append(f"• {strength.title()}")
            response_parts.append("")
        
        # Areas for improvement
        weaknesses = analysis_data.get('weaknesses', [])
        if weaknesses:
            response_parts.append("⚠️ **Areas for Improvement:**")
            for weakness in weaknesses:
                response_parts.append(f"• {weakness.title()}")
            response_parts.append("")
        
        # Recommendations
        recommendations = analysis_data.get('recommendations', [])
        if recommendations:
            response_parts.append("🔧 **Specific Recommendations:**")
            for i, rec in enumerate(recommendations[:8], 1):  # Limit to 8 recommendations
                response_parts.append(f"{i}. {rec}")
            
            if len(recommendations) > 8:
                response_parts.append(f"... and {len(recommendations) - 8} more recommendations")
        
        return "\n".join(response_parts)
    
    @staticmethod
    def _create_score_bar(score: float, length: int = 8) -> str:
        """Create a visual score bar."""
        filled = int((score / 100) * length)
        bar = "█" * filled + "░" * (length - filled)
        return f"[{bar}]"
    
    @staticmethod
    def format_error_response(error_message: str, suggestions: List[str] = None) -> str:
        """Format error responses with helpful suggestions."""
        response_parts = []
        
        response_parts.append("❌ **Error Encountered:**")
        response_parts.append(f"_{error_message}_\n")
        
        if suggestions:
            response_parts.append("💡 **Suggestions:**")
            for i, suggestion in enumerate(suggestions, 1):
                response_parts.append(f"{i}. {suggestion}")
        else:
            # Default suggestions
            response_parts.append("💡 **Suggestions:**")
            response_parts.append("1. Try rephrasing your request with simpler language")
            response_parts.append("2. Break complex requests into smaller steps")
            response_parts.append("3. Check that I have the necessary permissions")
            response_parts.append("4. Verify that mentioned channels/roles exist")
        
        return "\n".join(response_parts)
    
    @staticmethod
    def format_clarification_request(unclear_parts: List[str], context: str = "") -> str:
        """Format clarification requests for ambiguous commands."""
        response_parts = []
        
        response_parts.append("🤔 **I need clarification on your request:**")
        if context:
            response_parts.append(f"_{context}_\n")
        
        response_parts.append("❓ **Please clarify:**")
        for i, part in enumerate(unclear_parts, 1):
            response_parts.append(f"{i}. {part}")
        
        response_parts.append("\n💬 **Example:** Try being more specific about names, permissions, or organization structure.")
        
        return "\n".join(response_parts)
    
    @staticmethod
    def format_simple_response(message: str, emoji: str = "ℹ️") -> str:
        """Format simple responses with consistent styling."""
        return f"{emoji} {message}"
